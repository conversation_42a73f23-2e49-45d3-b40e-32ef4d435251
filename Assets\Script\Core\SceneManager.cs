using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace GameFramework.Core
{
    /// <summary>
    /// 场景管理器
    /// 负责场景的加载、卸载和切换管理
    /// </summary>
    public class SceneManager : MonoBehaviour, ISceneManager
    {
        [Header("场景管理器设置")]
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private float minLoadingTime = 1f; // 最小加载时间，避免加载过快

        private string currentSceneName;
        private List<string> loadedScenes = new List<string>();
        private Dictionary<string, AsyncOperation> loadingOperations = new Dictionary<string, AsyncOperation>();

        public string Name => "SceneManager";
        public bool IsInitialized { get; private set; }
        public string CurrentSceneName => currentSceneName;

        public event Action<float> OnSceneLoadProgress;
        public event Action<string> OnSceneLoaded;

        private void Awake()
        {
            // 确保单例
            if (FindObjectsOfType<SceneManager>().Length > 1)
            {
                Destroy(gameObject);
                return;
            }
            DontDestroyOnLoad(gameObject);
        }

        private void Start()
        {
            Initialize();
        }

        public void Initialize()
        {
            if (IsInitialized)
                return;

            LogDebug("初始化场景管理器");

            // 获取当前场景名称
            currentSceneName = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
            if (!loadedScenes.Contains(currentSceneName))
            {
                loadedScenes.Add(currentSceneName);
            }

            // 注册场景加载事件
            UnityEngine.SceneManagement.SceneManager.sceneLoaded += OnSceneLoadedCallback;
            UnityEngine.SceneManagement.SceneManager.sceneUnloaded += OnSceneUnloadedCallback;

            IsInitialized = true;
            LogDebug($"场景管理器初始化完成，当前场景: {currentSceneName}");
        }

        public void Update()
        {
            // 更新加载进度
            UpdateLoadingProgress();
        }

        public void Destroy()
        {
            LogDebug("销毁场景管理器");

            // 注销事件
            UnityEngine.SceneManagement.SceneManager.sceneLoaded -= OnSceneLoadedCallback;
            UnityEngine.SceneManagement.SceneManager.sceneUnloaded -= OnSceneUnloadedCallback;

            // 清理数据
            loadedScenes.Clear();
            loadingOperations.Clear();

            OnSceneLoadProgress = null;
            OnSceneLoaded = null;
            IsInitialized = false;
        }

        public void LoadScene(string sceneName, SceneLoadMode loadMode = SceneLoadMode.Single)
        {
            if (string.IsNullOrEmpty(sceneName))
            {
                LogDebug("场景名称不能为空");
                return;
            }

            LogDebug($"同步加载场景: {sceneName}, 模式: {loadMode}");

            try
            {
                LoadSceneMode unityLoadMode = loadMode == SceneLoadMode.Single ? 
                    LoadSceneMode.Single : LoadSceneMode.Additive;

                UnityEngine.SceneManagement.SceneManager.LoadScene(sceneName, unityLoadMode);

                if (loadMode == SceneLoadMode.Single)
                {
                    currentSceneName = sceneName;
                    loadedScenes.Clear();
                    loadedScenes.Add(sceneName);
                }
                else
                {
                    if (!loadedScenes.Contains(sceneName))
                    {
                        loadedScenes.Add(sceneName);
                    }
                }
            }
            catch (Exception e)
            {
                LogDebug($"加载场景失败: {sceneName}, 错误: {e.Message}");
            }
        }

        public void LoadSceneAsync(string sceneName, SceneLoadMode loadMode = SceneLoadMode.Single)
        {
            if (string.IsNullOrEmpty(sceneName))
            {
                LogDebug("场景名称不能为空");
                return;
            }

            if (loadingOperations.ContainsKey(sceneName))
            {
                LogDebug($"场景正在加载中: {sceneName}");
                return;
            }

            LogDebug($"异步加载场景: {sceneName}, 模式: {loadMode}");
            StartCoroutine(LoadSceneAsyncCoroutine(sceneName, loadMode));
        }

        public void UnloadScene(string sceneName)
        {
            if (string.IsNullOrEmpty(sceneName))
            {
                LogDebug("场景名称不能为空");
                return;
            }

            if (sceneName == currentSceneName)
            {
                LogDebug($"不能卸载当前主场景: {sceneName}");
                return;
            }

            if (!loadedScenes.Contains(sceneName))
            {
                LogDebug($"场景未加载: {sceneName}");
                return;
            }

            LogDebug($"卸载场景: {sceneName}");

            try
            {
                UnityEngine.SceneManagement.SceneManager.UnloadSceneAsync(sceneName);
                loadedScenes.Remove(sceneName);
            }
            catch (Exception e)
            {
                LogDebug($"卸载场景失败: {sceneName}, 错误: {e.Message}");
            }
        }

        /// <summary>
        /// 获取已加载的场景列表
        /// </summary>
        /// <returns>场景名称列表</returns>
        public List<string> GetLoadedScenes()
        {
            return new List<string>(loadedScenes);
        }

        /// <summary>
        /// 检查场景是否已加载
        /// </summary>
        /// <param name="sceneName">场景名称</param>
        /// <returns>是否已加载</returns>
        public bool IsSceneLoaded(string sceneName)
        {
            return loadedScenes.Contains(sceneName);
        }

        /// <summary>
        /// 异步加载场景协程
        /// </summary>
        private IEnumerator LoadSceneAsyncCoroutine(string sceneName, SceneLoadMode loadMode)
        {
            float startTime = Time.time;

            LoadSceneMode unityLoadMode = loadMode == SceneLoadMode.Single ? 
                LoadSceneMode.Single : LoadSceneMode.Additive;

            AsyncOperation operation = UnityEngine.SceneManagement.SceneManager.LoadSceneAsync(sceneName, unityLoadMode);
            loadingOperations[sceneName] = operation;

            // 防止场景自动激活
            operation.allowSceneActivation = false;

            // 等待加载完成
            while (operation.progress < 0.9f)
            {
                OnSceneLoadProgress?.Invoke(operation.progress);
                yield return null;
            }

            // 确保最小加载时间
            float elapsedTime = Time.time - startTime;
            if (elapsedTime < minLoadingTime)
            {
                yield return new WaitForSeconds(minLoadingTime - elapsedTime);
            }

            // 激活场景
            operation.allowSceneActivation = true;

            // 等待场景完全加载
            while (!operation.isDone)
            {
                yield return null;
            }

            // 更新场景信息
            if (loadMode == SceneLoadMode.Single)
            {
                currentSceneName = sceneName;
                loadedScenes.Clear();
                loadedScenes.Add(sceneName);
            }
            else
            {
                if (!loadedScenes.Contains(sceneName))
                {
                    loadedScenes.Add(sceneName);
                }
            }

            // 清理加载操作
            loadingOperations.Remove(sceneName);

            // 触发加载完成事件
            OnSceneLoaded?.Invoke(sceneName);
            OnSceneLoadProgress?.Invoke(1f);

            LogDebug($"场景加载完成: {sceneName}");
        }

        /// <summary>
        /// 更新加载进度
        /// </summary>
        private void UpdateLoadingProgress()
        {
            foreach (var operation in loadingOperations.Values)
            {
                if (operation != null && !operation.isDone)
                {
                    OnSceneLoadProgress?.Invoke(operation.progress);
                }
            }
        }

        /// <summary>
        /// 场景加载完成回调
        /// </summary>
        private void OnSceneLoadedCallback(Scene scene, LoadSceneMode mode)
        {
            LogDebug($"场景加载回调: {scene.name}, 模式: {mode}");
        }

        /// <summary>
        /// 场景卸载完成回调
        /// </summary>
        private void OnSceneUnloadedCallback(Scene scene)
        {
            LogDebug($"场景卸载回调: {scene.name}");
        }

        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[SceneManager] {message}");
            }
        }
    }
}
