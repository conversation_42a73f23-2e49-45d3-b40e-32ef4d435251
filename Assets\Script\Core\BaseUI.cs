using UnityEngine;
using UnityEngine.UI;

namespace GameFramework.Core
{
    /// <summary>
    /// UI基础类
    /// 所有UI组件的基类，提供通用功能
    /// </summary>
    public abstract class BaseUI : MonoBehaviour
    {
        [Header("UI基础设置")]
        [SerializeField] protected UIType uiType = UIType.Normal;
        [SerializeField] protected UILayer uiLayer = UILayer.Normal;
        [SerializeField] protected bool closeOnEscape = true;
        [SerializeField] protected bool playShowAnimation = true;
        [SerializeField] protected bool playHideAnimation = true;

        [Header("动画设置")]
        [SerializeField] protected float animationDuration = 0.3f;
        [SerializeField] protected AnimationCurve showCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        [SerializeField] protected AnimationCurve hideCurve = AnimationCurve.EaseInOut(0, 1, 1, 0);

        protected Canvas canvas;
        protected CanvasGroup canvasGroup;
        protected RectTransform rectTransform;
        protected bool isVisible = true;
        protected bool isAnimating = false;

        public UIType UIType => uiType;
        public UILayer UILayer => uiLayer;
        public bool IsVisible => isVisible;
        public bool IsAnimating => isAnimating;

        protected virtual void Awake()
        {
            InitializeComponents();
        }

        protected virtual void Start()
        {
            OnUICreated();
        }

        protected virtual void Update()
        {
            if (closeOnEscape && Input.GetKeyDown(KeyCode.Escape))
            {
                OnEscapePressed();
            }
        }

        protected virtual void OnDestroy()
        {
            OnUIDestroyed();
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        protected virtual void InitializeComponents()
        {
            rectTransform = GetComponent<RectTransform>();
            canvas = GetComponent<Canvas>();
            canvasGroup = GetComponent<CanvasGroup>();

            if (canvasGroup == null)
            {
                canvasGroup = gameObject.AddComponent<CanvasGroup>();
            }

            // 设置初始状态
            if (canvas != null)
            {
                canvas.overrideSorting = true;
            }
        }

        /// <summary>
        /// UI创建时调用
        /// </summary>
        protected virtual void OnUICreated()
        {
            Debug.Log($"[BaseUI] UI创建: {gameObject.name}");
        }

        /// <summary>
        /// UI销毁时调用
        /// </summary>
        protected virtual void OnUIDestroyed()
        {
            Debug.Log($"[BaseUI] UI销毁: {gameObject.name}");
        }

        /// <summary>
        /// 显示UI
        /// </summary>
        public virtual void Show()
        {
            if (isVisible || isAnimating)
                return;

            gameObject.SetActive(true);
            isVisible = true;

            OnBeforeShow();

            if (playShowAnimation)
            {
                PlayShowAnimation();
            }
            else
            {
                OnShowComplete();
            }
        }

        /// <summary>
        /// 隐藏UI
        /// </summary>
        public virtual void Hide()
        {
            if (!isVisible || isAnimating)
                return;

            isVisible = false;

            OnBeforeHide();

            if (playHideAnimation)
            {
                PlayHideAnimation();
            }
            else
            {
                OnHideComplete();
            }
        }

        /// <summary>
        /// 显示前调用
        /// </summary>
        protected virtual void OnBeforeShow()
        {
            Debug.Log($"[BaseUI] 显示UI: {gameObject.name}");
        }

        /// <summary>
        /// 显示完成后调用
        /// </summary>
        protected virtual void OnShowComplete()
        {
            Debug.Log($"[BaseUI] UI显示完成: {gameObject.name}");
        }

        /// <summary>
        /// 隐藏前调用
        /// </summary>
        protected virtual void OnBeforeHide()
        {
            Debug.Log($"[BaseUI] 隐藏UI: {gameObject.name}");
        }

        /// <summary>
        /// 隐藏完成后调用
        /// </summary>
        protected virtual void OnHideComplete()
        {
            gameObject.SetActive(false);
            Debug.Log($"[BaseUI] UI隐藏完成: {gameObject.name}");
        }

        /// <summary>
        /// Escape键按下时调用
        /// </summary>
        protected virtual void OnEscapePressed()
        {
            if (uiType == UIType.Popup || uiType == UIType.Dialog)
            {
                Hide();
            }
        }

        /// <summary>
        /// 播放显示动画
        /// </summary>
        protected virtual void PlayShowAnimation()
        {
            if (canvasGroup == null)
            {
                OnShowComplete();
                return;
            }

            isAnimating = true;
            canvasGroup.alpha = 0f;

            LeanTween.value(gameObject, 0f, 1f, animationDuration)
                .setEase(showCurve)
                .setOnUpdate((float value) =>
                {
                    if (canvasGroup != null)
                    {
                        canvasGroup.alpha = value;
                    }
                })
                .setOnComplete(() =>
                {
                    isAnimating = false;
                    OnShowComplete();
                });
        }

        /// <summary>
        /// 播放隐藏动画
        /// </summary>
        protected virtual void PlayHideAnimation()
        {
            if (canvasGroup == null)
            {
                OnHideComplete();
                return;
            }

            isAnimating = true;

            LeanTween.value(gameObject, canvasGroup.alpha, 0f, animationDuration)
                .setEase(hideCurve)
                .setOnUpdate((float value) =>
                {
                    if (canvasGroup != null)
                    {
                        canvasGroup.alpha = value;
                    }
                })
                .setOnComplete(() =>
                {
                    isAnimating = false;
                    OnHideComplete();
                });
        }

        /// <summary>
        /// 设置UI层级
        /// </summary>
        /// <param name="sortingOrder">排序顺序</param>
        public virtual void SetSortingOrder(int sortingOrder)
        {
            if (canvas != null)
            {
                canvas.sortingOrder = sortingOrder;
            }
        }

        /// <summary>
        /// 设置UI交互状态
        /// </summary>
        /// <param name="interactable">是否可交互</param>
        public virtual void SetInteractable(bool interactable)
        {
            if (canvasGroup != null)
            {
                canvasGroup.interactable = interactable;
                canvasGroup.blocksRaycasts = interactable;
            }
        }

        /// <summary>
        /// 设置UI透明度
        /// </summary>
        /// <param name="alpha">透明度值</param>
        public virtual void SetAlpha(float alpha)
        {
            if (canvasGroup != null)
            {
                canvasGroup.alpha = Mathf.Clamp01(alpha);
            }
        }

        /// <summary>
        /// 查找子组件
        /// </summary>
        /// <typeparam name="T">组件类型</typeparam>
        /// <param name="path">路径</param>
        /// <returns>组件实例</returns>
        protected T FindComponent<T>(string path) where T : Component
        {
            Transform child = transform.Find(path);
            return child?.GetComponent<T>();
        }

        /// <summary>
        /// 绑定按钮事件
        /// </summary>
        /// <param name="buttonPath">按钮路径</param>
        /// <param name="onClick">点击事件</param>
        protected void BindButton(string buttonPath, UnityEngine.Events.UnityAction onClick)
        {
            Button button = FindComponent<Button>(buttonPath);
            if (button != null)
            {
                button.onClick.AddListener(onClick);
            }
            else
            {
                Debug.LogWarning($"[BaseUI] 找不到按钮: {buttonPath}");
            }
        }

        /// <summary>
        /// 设置文本内容
        /// </summary>
        /// <param name="textPath">文本路径</param>
        /// <param name="content">文本内容</param>
        protected void SetText(string textPath, string content)
        {
            Text text = FindComponent<Text>(textPath);
            if (text != null)
            {
                text.text = content;
            }
            else
            {
                Debug.LogWarning($"[BaseUI] 找不到文本组件: {textPath}");
            }
        }

        /// <summary>
        /// 设置图片精灵
        /// </summary>
        /// <param name="imagePath">图片路径</param>
        /// <param name="sprite">精灵</param>
        protected void SetImage(string imagePath, Sprite sprite)
        {
            Image image = FindComponent<Image>(imagePath);
            if (image != null)
            {
                image.sprite = sprite;
            }
            else
            {
                Debug.LogWarning($"[BaseUI] 找不到图片组件: {imagePath}");
            }
        }
    }
}
