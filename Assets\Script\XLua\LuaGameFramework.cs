using System;
using UnityEngine;
using XLua;
using GameFramework.Core;

namespace GameFramework.XLua
{
    /// <summary>
    /// Lua游戏框架封装
    /// 为Lua提供游戏框架的主要接口
    /// </summary>
    public static class LuaGameFramework
    {
        /// <summary>
        /// 框架是否已初始化
        /// </summary>
        public static bool IsInitialized => GameFramework.Core.GameFramework.Instance.IsInitialized;

        /// <summary>
        /// 游戏状态管理器
        /// </summary>
        public static LuaGameStateManager GameState => LuaGameStateManager.Instance;

        /// <summary>
        /// 场景管理器
        /// </summary>
        public static LuaSceneManager Scene => LuaSceneManager.Instance;

        /// <summary>
        /// UI管理器
        /// </summary>
        public static LuaUIManager UI => LuaUIManager.Instance;

        /// <summary>
        /// 资源管理器
        /// </summary>
        public static LuaResourceManager Resource => LuaResourceManager.Instance;

        /// <summary>
        /// 输入管理器
        /// </summary>
        public static LuaInputManager Input => LuaInputManager.Instance;

        /// <summary>
        /// 事件系统
        /// </summary>
        public static LuaEventSystem Event => LuaEventSystem.Instance;

        /// <summary>
        /// 初始化框架
        /// </summary>
        public static void Initialize()
        {
            GameFramework.Core.GameFramework.Instance.Initialize();
        }

        /// <summary>
        /// 关闭框架
        /// </summary>
        public static void Shutdown()
        {
            GameFramework.Core.GameFramework.Instance.Shutdown();
        }

        /// <summary>
        /// 注册框架初始化完成回调
        /// </summary>
        /// <param name="callback">回调函数</param>
        public static void OnFrameworkInitialized(System.Action callback)
        {
            if (IsInitialized)
            {
                callback?.Invoke();
            }
            else
            {
                GameFramework.Core.GameFramework.OnFrameworkInitialized += callback;
            }
        }

        /// <summary>
        /// 注册框架关闭回调
        /// </summary>
        /// <param name="callback">回调函数</param>
        public static void OnFrameworkShutdown(System.Action callback)
        {
            GameFramework.Core.GameFramework.OnFrameworkShutdown += callback;
        }

        /// <summary>
        /// 日志输出
        /// </summary>
        /// <param name="message">消息</param>
        public static void Log(string message)
        {
            Debug.Log($"[Lua] {message}");
        }

        /// <summary>
        /// 警告日志输出
        /// </summary>
        /// <param name="message">消息</param>
        public static void LogWarning(string message)
        {
            Debug.LogWarning($"[Lua] {message}");
        }

        /// <summary>
        /// 错误日志输出
        /// </summary>
        /// <param name="message">消息</param>
        public static void LogError(string message)
        {
            Debug.LogError($"[Lua] {message}");
        }

        /// <summary>
        /// 获取当前时间
        /// </summary>
        /// <returns>当前时间戳</returns>
        public static float GetTime()
        {
            return Time.time;
        }

        /// <summary>
        /// 获取增量时间
        /// </summary>
        /// <returns>增量时间</returns>
        public static float GetDeltaTime()
        {
            return Time.deltaTime;
        }

        /// <summary>
        /// 获取固定增量时间
        /// </summary>
        /// <returns>固定增量时间</returns>
        public static float GetFixedDeltaTime()
        {
            return Time.fixedDeltaTime;
        }

        /// <summary>
        /// 获取不受时间缩放影响的时间
        /// </summary>
        /// <returns>真实时间</returns>
        public static float GetUnscaledTime()
        {
            return Time.unscaledTime;
        }

        /// <summary>
        /// 获取不受时间缩放影响的增量时间
        /// </summary>
        /// <returns>真实增量时间</returns>
        public static float GetUnscaledDeltaTime()
        {
            return Time.unscaledDeltaTime;
        }

        /// <summary>
        /// 设置时间缩放
        /// </summary>
        /// <param name="scale">时间缩放值</param>
        public static void SetTimeScale(float scale)
        {
            Time.timeScale = scale;
        }

        /// <summary>
        /// 获取时间缩放
        /// </summary>
        /// <returns>时间缩放值</returns>
        public static float GetTimeScale()
        {
            return Time.timeScale;
        }

        /// <summary>
        /// 暂停游戏
        /// </summary>
        public static void PauseGame()
        {
            Time.timeScale = 0f;
        }

        /// <summary>
        /// 恢复游戏
        /// </summary>
        public static void ResumeGame()
        {
            Time.timeScale = 1f;
        }

        /// <summary>
        /// 退出应用程序
        /// </summary>
        public static void QuitApplication()
        {
            Application.Quit();
        }

        /// <summary>
        /// 获取平台信息
        /// </summary>
        /// <returns>平台名称</returns>
        public static string GetPlatform()
        {
            return Application.platform.ToString();
        }

        /// <summary>
        /// 获取应用程序版本
        /// </summary>
        /// <returns>版本号</returns>
        public static string GetVersion()
        {
            return Application.version;
        }

        /// <summary>
        /// 获取数据路径
        /// </summary>
        /// <returns>数据路径</returns>
        public static string GetDataPath()
        {
            return Application.dataPath;
        }

        /// <summary>
        /// 获取持久化数据路径
        /// </summary>
        /// <returns>持久化数据路径</returns>
        public static string GetPersistentDataPath()
        {
            return Application.persistentDataPath;
        }

        /// <summary>
        /// 获取临时缓存路径
        /// </summary>
        /// <returns>临时缓存路径</returns>
        public static string GetTemporaryCachePath()
        {
            return Application.temporaryCachePath;
        }

        /// <summary>
        /// 获取StreamingAssets路径
        /// </summary>
        /// <returns>StreamingAssets路径</returns>
        public static string GetStreamingAssetsPath()
        {
            return Application.streamingAssetsPath;
        }

        /// <summary>
        /// 获取屏幕宽度
        /// </summary>
        /// <returns>屏幕宽度</returns>
        public static int GetScreenWidth()
        {
            return Screen.width;
        }

        /// <summary>
        /// 获取屏幕高度
        /// </summary>
        /// <returns>屏幕高度</returns>
        public static int GetScreenHeight()
        {
            return Screen.height;
        }

        /// <summary>
        /// 获取屏幕DPI
        /// </summary>
        /// <returns>屏幕DPI</returns>
        public static float GetScreenDPI()
        {
            return Screen.dpi;
        }

        /// <summary>
        /// 设置目标帧率
        /// </summary>
        /// <param name="frameRate">目标帧率</param>
        public static void SetTargetFrameRate(int frameRate)
        {
            Application.targetFrameRate = frameRate;
        }

        /// <summary>
        /// 获取当前帧率
        /// </summary>
        /// <returns>当前帧率</returns>
        public static float GetFrameRate()
        {
            return 1f / Time.deltaTime;
        }

        /// <summary>
        /// 生成随机数
        /// </summary>
        /// <param name="min">最小值</param>
        /// <param name="max">最大值</param>
        /// <returns>随机数</returns>
        public static float Random(float min, float max)
        {
            return UnityEngine.Random.Range(min, max);
        }

        /// <summary>
        /// 生成随机整数
        /// </summary>
        /// <param name="min">最小值</param>
        /// <param name="max">最大值</param>
        /// <returns>随机整数</returns>
        public static int RandomInt(int min, int max)
        {
            return UnityEngine.Random.Range(min, max);
        }

        /// <summary>
        /// 设置随机种子
        /// </summary>
        /// <param name="seed">种子值</param>
        public static void SetRandomSeed(int seed)
        {
            UnityEngine.Random.InitState(seed);
        }
    }
}
