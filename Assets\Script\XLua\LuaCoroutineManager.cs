using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using XLua;

namespace GameFramework.XLua
{
    /// <summary>
    /// Lua协程管理器
    /// 为Lua提供协程和定时器功能
    /// </summary>
    public class LuaCoroutineManager : MonoBehaviour
    {
        private static LuaCoroutineManager instance;
        private Dictionary<int, Coroutine> runningCoroutines = new Dictionary<int, Coroutine>();
        private Dictionary<int, LuaTimer> runningTimers = new Dictionary<int, LuaTimer>();
        private int nextCoroutineId = 1;
        private int nextTimerId = 1;

        public static LuaCoroutineManager Instance
        {
            get
            {
                if (instance == null)
                {
                    GameObject go = new GameObject("LuaCoroutineManager");
                    instance = go.AddComponent<LuaCoroutineManager>();
                    DontDestroyOnLoad(go);
                }
                return instance;
            }
        }

        private void Awake()
        {
            if (instance != null && instance != this)
            {
                Destroy(gameObject);
                return;
            }
            instance = this;
            DontDestroyOnLoad(gameObject);
        }

        private void Update()
        {
            UpdateTimers();
        }

        /// <summary>
        /// 启动延迟动作
        /// </summary>
        /// <param name="delay">延迟时间（秒）</param>
        /// <param name="action">要执行的动作</param>
        /// <returns>协程ID</returns>
        public int StartDelayedAction(float delay, System.Action action)
        {
            int coroutineId = nextCoroutineId++;
            Coroutine coroutine = StartCoroutine(DelayedActionCoroutine(delay, action, coroutineId));
            runningCoroutines[coroutineId] = coroutine;
            return coroutineId;
        }

        /// <summary>
        /// 启动重复动作
        /// </summary>
        /// <param name="interval">间隔时间（秒）</param>
        /// <param name="action">要执行的动作</param>
        /// <param name="repeatCount">重复次数（-1为无限重复）</param>
        /// <returns>协程ID</returns>
        public int StartRepeatingAction(float interval, System.Action action, int repeatCount = -1)
        {
            int coroutineId = nextCoroutineId++;
            Coroutine coroutine = StartCoroutine(RepeatingActionCoroutine(interval, action, repeatCount, coroutineId));
            runningCoroutines[coroutineId] = coroutine;
            return coroutineId;
        }

        /// <summary>
        /// 启动条件等待
        /// </summary>
        /// <param name="condition">等待条件</param>
        /// <param name="action">条件满足时执行的动作</param>
        /// <param name="timeout">超时时间（秒，-1为无超时）</param>
        /// <returns>协程ID</returns>
        public int StartWaitForCondition(System.Func<bool> condition, System.Action action, float timeout = -1)
        {
            int coroutineId = nextCoroutineId++;
            Coroutine coroutine = StartCoroutine(WaitForConditionCoroutine(condition, action, timeout, coroutineId));
            runningCoroutines[coroutineId] = coroutine;
            return coroutineId;
        }

        /// <summary>
        /// 启动渐变动作
        /// </summary>
        /// <param name="duration">持续时间（秒）</param>
        /// <param name="onUpdate">更新回调（参数为0-1的进度值）</param>
        /// <param name="onComplete">完成回调</param>
        /// <returns>协程ID</returns>
        public int StartTween(float duration, System.Action<float> onUpdate, System.Action onComplete = null)
        {
            int coroutineId = nextCoroutineId++;
            Coroutine coroutine = StartCoroutine(TweenCoroutine(duration, onUpdate, onComplete, coroutineId));
            runningCoroutines[coroutineId] = coroutine;
            return coroutineId;
        }

        /// <summary>
        /// 停止协程
        /// </summary>
        /// <param name="coroutineId">协程ID</param>
        public void StopCoroutineById(int coroutineId)
        {
            if (runningCoroutines.ContainsKey(coroutineId))
            {
                StopCoroutine(runningCoroutines[coroutineId]);
                runningCoroutines.Remove(coroutineId);
                Debug.Log($"[LuaCoroutineManager] 停止协程: {coroutineId}");
            }
        }

        /// <summary>
        /// 停止所有协程
        /// </summary>
        public void StopAllCoroutines()
        {
            foreach (var coroutine in runningCoroutines.Values)
            {
                StopCoroutine(coroutine);
            }
            runningCoroutines.Clear();
            Debug.Log("[LuaCoroutineManager] 停止所有协程");
        }

        /// <summary>
        /// 创建定时器
        /// </summary>
        /// <param name="interval">间隔时间（秒）</param>
        /// <param name="action">要执行的动作</param>
        /// <param name="repeatCount">重复次数（-1为无限重复）</param>
        /// <param name="startImmediately">是否立即开始</param>
        /// <returns>定时器ID</returns>
        public int CreateTimer(float interval, System.Action action, int repeatCount = -1, bool startImmediately = true)
        {
            int timerId = nextTimerId++;
            LuaTimer timer = new LuaTimer
            {
                id = timerId,
                interval = interval,
                action = action,
                repeatCount = repeatCount,
                currentCount = 0,
                isActive = startImmediately,
                lastTriggerTime = startImmediately ? Time.time : 0f
            };

            runningTimers[timerId] = timer;
            Debug.Log($"[LuaCoroutineManager] 创建定时器: {timerId}, 间隔: {interval}秒");
            return timerId;
        }

        /// <summary>
        /// 启动定时器
        /// </summary>
        /// <param name="timerId">定时器ID</param>
        public void StartTimer(int timerId)
        {
            if (runningTimers.ContainsKey(timerId))
            {
                runningTimers[timerId].isActive = true;
                runningTimers[timerId].lastTriggerTime = Time.time;
                Debug.Log($"[LuaCoroutineManager] 启动定时器: {timerId}");
            }
        }

        /// <summary>
        /// 停止定时器
        /// </summary>
        /// <param name="timerId">定时器ID</param>
        public void StopTimer(int timerId)
        {
            if (runningTimers.ContainsKey(timerId))
            {
                runningTimers[timerId].isActive = false;
                Debug.Log($"[LuaCoroutineManager] 停止定时器: {timerId}");
            }
        }

        /// <summary>
        /// 销毁定时器
        /// </summary>
        /// <param name="timerId">定时器ID</param>
        public void DestroyTimer(int timerId)
        {
            if (runningTimers.ContainsKey(timerId))
            {
                runningTimers.Remove(timerId);
                Debug.Log($"[LuaCoroutineManager] 销毁定时器: {timerId}");
            }
        }

        /// <summary>
        /// 销毁所有定时器
        /// </summary>
        public void DestroyAllTimers()
        {
            runningTimers.Clear();
            Debug.Log("[LuaCoroutineManager] 销毁所有定时器");
        }

        /// <summary>
        /// 检查协程是否在运行
        /// </summary>
        /// <param name="coroutineId">协程ID</param>
        /// <returns>是否在运行</returns>
        public bool IsCoroutineRunning(int coroutineId)
        {
            return runningCoroutines.ContainsKey(coroutineId);
        }

        /// <summary>
        /// 检查定时器是否激活
        /// </summary>
        /// <param name="timerId">定时器ID</param>
        /// <returns>是否激活</returns>
        public bool IsTimerActive(int timerId)
        {
            return runningTimers.ContainsKey(timerId) && runningTimers[timerId].isActive;
        }

        /// <summary>
        /// 获取运行中的协程数量
        /// </summary>
        /// <returns>协程数量</returns>
        public int GetRunningCoroutineCount()
        {
            return runningCoroutines.Count;
        }

        /// <summary>
        /// 获取运行中的定时器数量
        /// </summary>
        /// <returns>定时器数量</returns>
        public int GetRunningTimerCount()
        {
            return runningTimers.Count;
        }

        // 协程实现
        private IEnumerator DelayedActionCoroutine(float delay, System.Action action, int coroutineId)
        {
            yield return new WaitForSeconds(delay);
            
            try
            {
                action?.Invoke();
            }
            catch (Exception e)
            {
                Debug.LogError($"[LuaCoroutineManager] 延迟动作异常: {e.Message}");
            }
            
            runningCoroutines.Remove(coroutineId);
        }

        private IEnumerator RepeatingActionCoroutine(float interval, System.Action action, int repeatCount, int coroutineId)
        {
            int currentCount = 0;
            
            while (repeatCount == -1 || currentCount < repeatCount)
            {
                yield return new WaitForSeconds(interval);
                
                try
                {
                    action?.Invoke();
                }
                catch (Exception e)
                {
                    Debug.LogError($"[LuaCoroutineManager] 重复动作异常: {e.Message}");
                }
                
                currentCount++;
            }
            
            runningCoroutines.Remove(coroutineId);
        }

        private IEnumerator WaitForConditionCoroutine(System.Func<bool> condition, System.Action action, float timeout, int coroutineId)
        {
            float startTime = Time.time;
            
            while (true)
            {
                try
                {
                    if (condition?.Invoke() == true)
                    {
                        action?.Invoke();
                        break;
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"[LuaCoroutineManager] 条件检查异常: {e.Message}");
                    break;
                }
                
                if (timeout > 0 && Time.time - startTime >= timeout)
                {
                    Debug.LogWarning($"[LuaCoroutineManager] 条件等待超时: {timeout}秒");
                    break;
                }
                
                yield return null;
            }
            
            runningCoroutines.Remove(coroutineId);
        }

        private IEnumerator TweenCoroutine(float duration, System.Action<float> onUpdate, System.Action onComplete, int coroutineId)
        {
            float startTime = Time.time;
            
            while (Time.time - startTime < duration)
            {
                float progress = (Time.time - startTime) / duration;
                
                try
                {
                    onUpdate?.Invoke(progress);
                }
                catch (Exception e)
                {
                    Debug.LogError($"[LuaCoroutineManager] 渐变更新异常: {e.Message}");
                }
                
                yield return null;
            }
            
            try
            {
                onUpdate?.Invoke(1f);
                onComplete?.Invoke();
            }
            catch (Exception e)
            {
                Debug.LogError($"[LuaCoroutineManager] 渐变完成异常: {e.Message}");
            }
            
            runningCoroutines.Remove(coroutineId);
        }

        private void UpdateTimers()
        {
            var timersToRemove = new List<int>();
            
            foreach (var timer in runningTimers.Values)
            {
                if (!timer.isActive)
                    continue;
                
                if (Time.time - timer.lastTriggerTime >= timer.interval)
                {
                    try
                    {
                        timer.action?.Invoke();
                    }
                    catch (Exception e)
                    {
                        Debug.LogError($"[LuaCoroutineManager] 定时器动作异常: {e.Message}");
                    }
                    
                    timer.lastTriggerTime = Time.time;
                    timer.currentCount++;
                    
                    if (timer.repeatCount > 0 && timer.currentCount >= timer.repeatCount)
                    {
                        timersToRemove.Add(timer.id);
                    }
                }
            }
            
            foreach (int timerId in timersToRemove)
            {
                runningTimers.Remove(timerId);
            }
        }

        private void OnDestroy()
        {
            StopAllCoroutines();
            DestroyAllTimers();
        }
    }

    /// <summary>
    /// Lua定时器类
    /// </summary>
    public class LuaTimer
    {
        public int id;
        public float interval;
        public System.Action action;
        public int repeatCount;
        public int currentCount;
        public bool isActive;
        public float lastTriggerTime;
    }

    /// <summary>
    /// Lua协程辅助类
    /// </summary>
    public static class LuaCoroutineHelper
    {
        /// <summary>
        /// 延迟执行
        /// </summary>
        /// <param name="delay">延迟时间（秒）</param>
        /// <param name="action">要执行的动作</param>
        /// <returns>协程ID</returns>
        public static int Delay(float delay, System.Action action)
        {
            return LuaCoroutineManager.Instance.StartDelayedAction(delay, action);
        }

        /// <summary>
        /// 重复执行
        /// </summary>
        /// <param name="interval">间隔时间（秒）</param>
        /// <param name="action">要执行的动作</param>
        /// <param name="count">重复次数</param>
        /// <returns>协程ID</returns>
        public static int Repeat(float interval, System.Action action, int count = -1)
        {
            return LuaCoroutineManager.Instance.StartRepeatingAction(interval, action, count);
        }

        /// <summary>
        /// 等待条件
        /// </summary>
        /// <param name="condition">等待条件</param>
        /// <param name="action">条件满足时执行的动作</param>
        /// <param name="timeout">超时时间</param>
        /// <returns>协程ID</returns>
        public static int WaitFor(System.Func<bool> condition, System.Action action, float timeout = -1)
        {
            return LuaCoroutineManager.Instance.StartWaitForCondition(condition, action, timeout);
        }

        /// <summary>
        /// 渐变动画
        /// </summary>
        /// <param name="duration">持续时间</param>
        /// <param name="onUpdate">更新回调</param>
        /// <param name="onComplete">完成回调</param>
        /// <returns>协程ID</returns>
        public static int Tween(float duration, System.Action<float> onUpdate, System.Action onComplete = null)
        {
            return LuaCoroutineManager.Instance.StartTween(duration, onUpdate, onComplete);
        }

        /// <summary>
        /// 停止协程
        /// </summary>
        /// <param name="coroutineId">协程ID</param>
        public static void Stop(int coroutineId)
        {
            LuaCoroutineManager.Instance.StopCoroutineById(coroutineId);
        }

        /// <summary>
        /// 创建定时器
        /// </summary>
        /// <param name="interval">间隔时间</param>
        /// <param name="action">要执行的动作</param>
        /// <param name="count">重复次数</param>
        /// <returns>定时器ID</returns>
        public static int CreateTimer(float interval, System.Action action, int count = -1)
        {
            return LuaCoroutineManager.Instance.CreateTimer(interval, action, count);
        }

        /// <summary>
        /// 销毁定时器
        /// </summary>
        /// <param name="timerId">定时器ID</param>
        public static void DestroyTimer(int timerId)
        {
            LuaCoroutineManager.Instance.DestroyTimer(timerId);
        }
    }
}
