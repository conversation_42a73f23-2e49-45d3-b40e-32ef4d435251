using System;
using System.Collections.Generic;
using UnityEngine;

namespace GameFramework.Core
{
    /// <summary>
    /// 游戏框架主入口
    /// 统一初始化和管理所有管理器
    /// </summary>
    public class GameFramework : MonoBehaviour
    {
        [Header("框架设置")]
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private bool autoInitialize = true;
        [SerializeField] private bool dontDestroyOnLoad = true;

        [Header("管理器设置")]
        [SerializeField] private bool enableGameStateManager = true;
        [SerializeField] private bool enableSceneManager = true;
        [SerializeField] private bool enableUIManager = true;
        [SerializeField] private bool enableResourceManager = true;
        [SerializeField] private bool enableInputManager = true;

        private static GameFramework instance;
        private Dictionary<Type, IManager> managers = new Dictionary<Type, IManager>();
        private bool isInitialized = false;

        // 管理器实例
        private GameStateManager gameStateManager;
        private SceneManager sceneManager;
        private UIManager uiManager;
        private ResourceManager resourceManager;
        private InputManager inputManager;

        public static GameFramework Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = FindObjectOfType<GameFramework>();
                    if (instance == null)
                    {
                        GameObject go = new GameObject("GameFramework");
                        instance = go.AddComponent<GameFramework>();
                    }
                }
                return instance;
            }
        }

        public bool IsInitialized => isInitialized;

        // 管理器访问属性
        public static IGameStateManager GameState => Instance.GetManager<IGameStateManager>();
        public static ISceneManager Scene => Instance.GetManager<ISceneManager>();
        public static IUIManager UI => Instance.GetManager<IUIManager>();
        public static IResourceManager Resource => Instance.GetManager<IResourceManager>();
        public static IInputManager Input => Instance.GetManager<IInputManager>();

        private void Awake()
        {
            // 确保单例
            if (instance != null && instance != this)
            {
                Destroy(gameObject);
                return;
            }

            instance = this;

            if (dontDestroyOnLoad)
            {
                DontDestroyOnLoad(gameObject);
            }

            if (autoInitialize)
            {
                Initialize();
            }
        }

        private void Start()
        {
            if (!isInitialized && autoInitialize)
            {
                Initialize();
            }
        }

        private void Update()
        {
            if (isInitialized)
            {
                // 更新所有管理器
                foreach (var manager in managers.Values)
                {
                    if (manager.IsInitialized)
                    {
                        manager.Update();
                    }
                }
            }
        }

        private void OnDestroy()
        {
            Shutdown();
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            LogDebug($"应用程序暂停状态: {pauseStatus}");
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            LogDebug($"应用程序焦点状态: {hasFocus}");
        }

        private void OnApplicationQuit()
        {
            LogDebug("应用程序退出");
            Shutdown();
        }

        /// <summary>
        /// 初始化框架
        /// </summary>
        public void Initialize()
        {
            if (isInitialized)
            {
                LogDebug("框架已经初始化");
                return;
            }

            LogDebug("开始初始化游戏框架");

            try
            {
                // 初始化各个管理器
                InitializeManagers();

                isInitialized = true;
                LogDebug("游戏框架初始化完成");

                // 触发初始化完成事件
                OnFrameworkInitialized?.Invoke();
            }
            catch (Exception e)
            {
                LogDebug($"框架初始化失败: {e.Message}");
                Debug.LogException(e);
            }
        }

        /// <summary>
        /// 关闭框架
        /// </summary>
        public void Shutdown()
        {
            if (!isInitialized)
                return;

            LogDebug("开始关闭游戏框架");

            try
            {
                // 销毁所有管理器
                foreach (var manager in managers.Values)
                {
                    if (manager.IsInitialized)
                    {
                        manager.Destroy();
                    }
                }

                managers.Clear();
                isInitialized = false;

                LogDebug("游戏框架关闭完成");

                // 触发关闭完成事件
                OnFrameworkShutdown?.Invoke();
            }
            catch (Exception e)
            {
                LogDebug($"框架关闭失败: {e.Message}");
                Debug.LogException(e);
            }
        }

        /// <summary>
        /// 获取管理器
        /// </summary>
        /// <typeparam name="T">管理器类型</typeparam>
        /// <returns>管理器实例</returns>
        public T GetManager<T>() where T : class, IManager
        {
            Type type = typeof(T);
            if (managers.ContainsKey(type))
            {
                return managers[type] as T;
            }
            return null;
        }

        /// <summary>
        /// 注册管理器
        /// </summary>
        /// <typeparam name="T">管理器接口类型</typeparam>
        /// <param name="manager">管理器实例</param>
        public void RegisterManager<T>(T manager) where T : class, IManager
        {
            Type type = typeof(T);
            if (managers.ContainsKey(type))
            {
                LogDebug($"替换管理器: {type.Name}");
                managers[type].Destroy();
            }

            managers[type] = manager;
            LogDebug($"注册管理器: {type.Name}");

            if (isInitialized && !manager.IsInitialized)
            {
                manager.Initialize();
            }
        }

        /// <summary>
        /// 注销管理器
        /// </summary>
        /// <typeparam name="T">管理器类型</typeparam>
        public void UnregisterManager<T>() where T : class, IManager
        {
            Type type = typeof(T);
            if (managers.ContainsKey(type))
            {
                managers[type].Destroy();
                managers.Remove(type);
                LogDebug($"注销管理器: {type.Name}");
            }
        }

        /// <summary>
        /// 初始化所有管理器
        /// </summary>
        private void InitializeManagers()
        {
            // 初始化资源管理器（优先级最高）
            if (enableResourceManager)
            {
                InitializeResourceManager();
            }

            // 初始化输入管理器
            if (enableInputManager)
            {
                InitializeInputManager();
            }

            // 初始化场景管理器
            if (enableSceneManager)
            {
                InitializeSceneManager();
            }

            // 初始化UI管理器
            if (enableUIManager)
            {
                InitializeUIManager();
            }

            // 初始化游戏状态管理器（最后初始化）
            if (enableGameStateManager)
            {
                InitializeGameStateManager();
            }

            LogDebug($"管理器初始化完成，共 {managers.Count} 个管理器");
        }

        /// <summary>
        /// 初始化游戏状态管理器
        /// </summary>
        private void InitializeGameStateManager()
        {
            if (gameStateManager == null)
            {
                GameObject go = new GameObject("GameStateManager");
                go.transform.SetParent(transform);
                gameStateManager = go.AddComponent<GameStateManager>();
            }

            RegisterManager<IGameStateManager>(gameStateManager);
            LogDebug("游戏状态管理器初始化完成");
        }

        /// <summary>
        /// 初始化场景管理器
        /// </summary>
        private void InitializeSceneManager()
        {
            if (sceneManager == null)
            {
                GameObject go = new GameObject("SceneManager");
                go.transform.SetParent(transform);
                sceneManager = go.AddComponent<SceneManager>();
            }

            RegisterManager<ISceneManager>(sceneManager);
            LogDebug("场景管理器初始化完成");
        }

        /// <summary>
        /// 初始化UI管理器
        /// </summary>
        private void InitializeUIManager()
        {
            if (uiManager == null)
            {
                GameObject go = new GameObject("UIManager");
                go.transform.SetParent(transform);
                uiManager = go.AddComponent<UIManager>();
            }

            RegisterManager<IUIManager>(uiManager);
            LogDebug("UI管理器初始化完成");
        }

        /// <summary>
        /// 初始化资源管理器
        /// </summary>
        private void InitializeResourceManager()
        {
            if (resourceManager == null)
            {
                GameObject go = new GameObject("ResourceManager");
                go.transform.SetParent(transform);
                resourceManager = go.AddComponent<ResourceManager>();
            }

            RegisterManager<IResourceManager>(resourceManager);
            LogDebug("资源管理器初始化完成");
        }

        /// <summary>
        /// 初始化输入管理器
        /// </summary>
        private void InitializeInputManager()
        {
            if (inputManager == null)
            {
                GameObject go = new GameObject("InputManager");
                go.transform.SetParent(transform);
                inputManager = go.AddComponent<InputManager>();
            }

            RegisterManager<IInputManager>(inputManager);
            LogDebug("输入管理器初始化完成");
        }

        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[GameFramework] {message}");
            }
        }

        // 事件
        public static event Action OnFrameworkInitialized;
        public static event Action OnFrameworkShutdown;
    }
}
