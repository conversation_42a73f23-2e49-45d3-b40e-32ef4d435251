using UnityEngine;
using GameFramework.Core;

namespace GameFramework.Examples
{
    /// <summary>
    /// 输入系统使用示例
    /// 展示如何使用InputManager和InputEventSystem
    /// </summary>
    public class InputExample : MonoBehaviour
    {
        [Header("示例设置")]
        [SerializeField] private bool enableDebugLog = true;

        private void Start()
        {
            LogDebug("输入系统示例启动");

            // 等待框架初始化
            if (Core.GameFramework.Instance.IsInitialized)
            {
                OnFrameworkReady();
            }
            else
            {
                Core.GameFramework.OnFrameworkInitialized += OnFrameworkReady;
            }
        }

        private void OnFrameworkReady()
        {
            LogDebug("框架初始化完成，输入系统可用");

            // 监听设备切换事件
            if (Core.GameFramework.Input != null)
            {
                Core.GameFramework.Input.OnDeviceChanged += OnInputDeviceChanged;
            }
        }

        private void Update()
        {
            if (Core.GameFramework.Input == null || !Core.GameFramework.Input.InputEnabled)
                return;

            // 简单的输入检测示例
            if (Core.GameFramework.Input.GetButtonDown(InputActionType.Jump))
            {
                LogDebug("检测到跳跃输入");
            }

            if (Core.GameFramework.Input.GetButtonDown(InputActionType.Attack))
            {
                LogDebug("检测到攻击输入");
            }

            Vector2 moveInput = Core.GameFramework.Input.GetVector2(InputActionType.Move);
            if (moveInput != Vector2.zero)
            {
                LogDebug($"移动输入: {moveInput}");
            }
        }

        private void OnInputDeviceChanged(InputDeviceType deviceType)
        {
            LogDebug($"输入设备切换: {deviceType}");
        }

        private void OnDestroy()
        {
            // 清理事件
            if (Core.GameFramework.Input != null)
            {
                Core.GameFramework.Input.OnDeviceChanged -= OnInputDeviceChanged;
            }
            Core.GameFramework.OnFrameworkInitialized -= OnFrameworkReady;
        }

        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[InputExample] {message}");
            }
        }

        private void OnGUI()
        {
            // 显示输入状态信息
            GUILayout.BeginArea(new Rect(10, 250, 300, 200));
            GUILayout.Label("输入系统示例");

            if (Core.GameFramework.Input != null)
            {
                GUILayout.Label($"当前设备: {Core.GameFramework.Input.CurrentDeviceType}");
                GUILayout.Label($"输入启用: {Core.GameFramework.Input.InputEnabled}");
            }

            GUILayout.Space(10);
            GUILayout.Label("控制说明:");
            GUILayout.Label("WASD/左摇杆 - 移动");
            GUILayout.Label("空格/A键 - 跳跃");
            GUILayout.Label("鼠标左键/X键 - 攻击");
            GUILayout.EndArea();
        }
    }
}

        /// <summary>
        /// 处理移动输入
        /// </summary>
        private void HandleMovementInput()
        {
            if (playerTransform == null)
                return;

            // 获取移动输入
            Vector2 currentMoveInput = GameFramework.Input.GetVector2(InputActionType.Move);
            
            if (currentMoveInput != Vector2.zero)
            {
                // 计算移动方向
                Vector3 moveDirection = new Vector3(currentMoveInput.x, 0, currentMoveInput.y);
                
                // 应用冲刺速度修正
                float currentSpeed = isSprinting ? moveSpeed * 2f : moveSpeed;
                
                // 移动玩家
                playerTransform.Translate(moveDirection * currentSpeed * Time.deltaTime);
            }
        }

        /// <summary>
        /// 处理视角输入
        /// </summary>
        private void HandleLookInput()
        {
            if (playerTransform == null)
                return;

            // 获取视角输入
            Vector2 currentLookInput = GameFramework.Input.GetVector2(InputActionType.Look);
            
            if (currentLookInput != Vector2.zero)
            {
                // 应用视角旋转
                float yaw = currentLookInput.x * lookSensitivity;
                playerTransform.Rotate(0, yaw, 0);
            }
        }

        /// <summary>
        /// 处理其他输入
        /// </summary>
        private void HandleOtherInputs()
        {
            // 检查跳跃输入
            if (GameFramework.Input.GetButtonDown(InputActionType.Jump))
            {
                LogDebug("跳跃输入检测");
            }

            // 检查攻击输入
            if (GameFramework.Input.GetButton(InputActionType.Attack))
            {
                LogDebug("攻击输入持续中");
            }

            // 检查交互输入
            if (GameFramework.Input.GetButtonUp(InputActionType.Interact))
            {
                LogDebug("交互输入释放");
            }
        }

        /// <summary>
        /// 移动输入事件
        /// </summary>
        private void OnMoveInput(InputActionType actionType, InputState state, object value)
        {
            if (value is Vector2 moveVector)
            {
                moveInput = moveVector;
                LogDebug($"移动输入: {moveVector}");
            }
        }

        /// <summary>
        /// 视角输入事件
        /// </summary>
        private void OnLookInput(InputActionType actionType, InputState state, object value)
        {
            if (value is Vector2 lookVector)
            {
                lookInput = lookVector;
                LogDebug($"视角输入: {lookVector}");
            }
        }

        /// <summary>
        /// 跳跃输入事件
        /// </summary>
        private void OnJumpInput(InputActionType actionType, InputState state, object value)
        {
            switch (state)
            {
                case InputState.Started:
                    isJumping = true;
                    LogDebug("开始跳跃");
                    break;
                case InputState.Canceled:
                    isJumping = false;
                    LogDebug("结束跳跃");
                    break;
            }
        }

        /// <summary>
        /// 冲刺输入事件
        /// </summary>
        private void OnSprintInput(InputActionType actionType, InputState state, object value)
        {
            switch (state)
            {
                case InputState.Started:
                    isSprinting = true;
                    LogDebug("开始冲刺");
                    break;
                case InputState.Canceled:
                    isSprinting = false;
                    LogDebug("结束冲刺");
                    break;
            }
        }

        /// <summary>
        /// 攻击输入事件
        /// </summary>
        private void OnAttackInput(InputActionType actionType, InputState state, object value)
        {
            if (state == InputState.Performed)
            {
                LogDebug("执行攻击");
                // 在这里添加攻击逻辑
            }
        }

        /// <summary>
        /// 交互输入事件
        /// </summary>
        private void OnInteractInput(InputActionType actionType, InputState state, object value)
        {
            if (state == InputState.Performed)
            {
                LogDebug("执行交互");
                // 在这里添加交互逻辑
            }
        }

        /// <summary>
        /// 取消输入事件
        /// </summary>
        private void OnCancelInput(InputActionType actionType, InputState state, object value)
        {
            if (state == InputState.Performed)
            {
                LogDebug("取消操作");
                // 切换到游戏输入模式
                SwitchToGameplayInput();
            }
        }

        /// <summary>
        /// 确认输入事件
        /// </summary>
        private void OnSubmitInput(InputActionType actionType, InputState state, object value)
        {
            if (state == InputState.Performed)
            {
                LogDebug("确认操作");
            }
        }

        /// <summary>
        /// 输入设备切换事件
        /// </summary>
        private void OnInputDeviceChanged(InputDeviceType deviceType)
        {
            LogDebug($"输入设备切换: {deviceType}");
            
            // 根据设备类型调整设置
            switch (deviceType)
            {
                case InputDeviceType.KeyboardMouse:
                    lookSensitivity = 2f;
                    break;
                case InputDeviceType.Gamepad:
                    lookSensitivity = 100f;
                    break;
                case InputDeviceType.Touch:
                    lookSensitivity = 1f;
                    break;
            }
        }

        /// <summary>
        /// 切换到游戏输入模式
        /// </summary>
        public void SwitchToGameplayInput()
        {
            if (inputEventSystem != null)
            {
                inputEventSystem.EnableEventGroup("Player");
                inputEventSystem.DisableEventGroup("UI");
            }

            if (GameFramework.Input != null)
            {
                GameFramework.Input.EnableActionMap("Player");
            }

            LogDebug("切换到游戏输入模式");
        }

        /// <summary>
        /// 切换到UI输入模式
        /// </summary>
        public void SwitchToUIInput()
        {
            if (inputEventSystem != null)
            {
                inputEventSystem.DisableEventGroup("Player");
                inputEventSystem.EnableEventGroup("UI");
            }

            if (GameFramework.Input != null)
            {
                GameFramework.Input.EnableActionMap("UI");
            }

            LogDebug("切换到UI输入模式");
        }

        /// <summary>
        /// 禁用所有输入
        /// </summary>
        public void DisableAllInput()
        {
            if (GameFramework.Input != null)
            {
                GameFramework.Input.InputEnabled = false;
            }

            LogDebug("禁用所有输入");
        }

        /// <summary>
        /// 启用所有输入
        /// </summary>
        public void EnableAllInput()
        {
            if (GameFramework.Input != null)
            {
                GameFramework.Input.InputEnabled = true;
            }

            LogDebug("启用所有输入");
        }

        private void OnDestroy()
        {
            // 清理事件
            if (GameFramework.Input != null)
            {
                GameFramework.Input.OnDeviceChanged -= OnInputDeviceChanged;
            }

            GameFramework.OnFrameworkInitialized -= OnFrameworkReady;
        }

        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[InputExample] {message}");
            }
        }

        private void OnGUI()
        {
            // 显示输入状态信息
            GUILayout.BeginArea(new Rect(10, 250, 300, 300));
            GUILayout.Label("输入系统示例");
            
            if (GameFramework.Input != null)
            {
                GUILayout.Label($"当前设备: {GameFramework.Input.CurrentDeviceType}");
                GUILayout.Label($"输入启用: {GameFramework.Input.InputEnabled}");
                GUILayout.Label($"移动输入: {moveInput}");
                GUILayout.Label($"视角输入: {lookInput}");
                GUILayout.Label($"跳跃状态: {isJumping}");
                GUILayout.Label($"冲刺状态: {isSprinting}");
            }

            GUILayout.Space(10);
            GUILayout.Label("控制说明:");
            GUILayout.Label("WASD/左摇杆 - 移动");
            GUILayout.Label("鼠标/右摇杆 - 视角");
            GUILayout.Label("空格/A键 - 跳跃");
            GUILayout.Label("Shift/左摇杆按下 - 冲刺");
            GUILayout.Label("鼠标左键/X键 - 攻击");
            GUILayout.Label("E键/Y键 - 交互");

            GUILayout.Space(10);
            if (GUILayout.Button("切换到游戏输入"))
            {
                SwitchToGameplayInput();
            }
            if (GUILayout.Button("切换到UI输入"))
            {
                SwitchToUIInput();
            }
            if (GUILayout.Button("禁用输入"))
            {
                DisableAllInput();
            }
            if (GUILayout.Button("启用输入"))
            {
                EnableAllInput();
            }

            GUILayout.EndArea();
        }
    }
}
