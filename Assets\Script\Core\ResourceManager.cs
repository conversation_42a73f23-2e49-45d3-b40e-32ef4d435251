using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEngine.Networking;

namespace GameFramework.Core
{
    /// <summary>
    /// 资源管理器
    /// 处理本地资源加载和远程资源下载、解压缩功能
    /// </summary>
    public class ResourceManager : MonoBehaviour, IResourceManager
    {
        [Header("资源管理器设置")]
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private string localResourcePath = "Resources/";
        [SerializeField] private string downloadPath = "";
        [SerializeField] private int maxConcurrentDownloads = 3;

        private Dictionary<string, UnityEngine.Object> loadedResources = new Dictionary<string, UnityEngine.Object>();
        private Dictionary<string, ResourceInfo> resourceInfos = new Dictionary<string, ResourceInfo>();
        private Queue<DownloadTask> downloadQueue = new Queue<DownloadTask>();
        private List<DownloadTask> activeDownloads = new List<DownloadTask>();

        public string Name => "ResourceManager";
        public bool IsInitialized { get; private set; }

        private void Awake()
        {
            // 确保单例
            if (FindObjectsOfType<ResourceManager>().Length > 1)
            {
                Destroy(gameObject);
                return;
            }
            DontDestroyOnLoad(gameObject);
        }

        private void Start()
        {
            Initialize();
        }

        public void Initialize()
        {
            if (IsInitialized)
                return;

            LogDebug("初始化资源管理器");

            // 设置下载路径
            if (string.IsNullOrEmpty(downloadPath))
            {
                downloadPath = Path.Combine(Application.persistentDataPath, "Downloads");
            }

            // 创建下载目录
            if (!Directory.Exists(downloadPath))
            {
                Directory.CreateDirectory(downloadPath);
            }

            IsInitialized = true;
            LogDebug($"资源管理器初始化完成，下载路径: {downloadPath}");
        }

        public void Update()
        {
            // 处理下载队列
            ProcessDownloadQueue();
        }

        public void Destroy()
        {
            LogDebug("销毁资源管理器");

            // 停止所有下载
            StopAllDownloads();

            // 卸载所有资源
            UnloadAllResources();

            IsInitialized = false;
        }

        public T LoadResource<T>(string path, LoadMode loadMode = LoadMode.Local) where T : UnityEngine.Object
        {
            if (string.IsNullOrEmpty(path))
            {
                LogDebug("资源路径不能为空");
                return null;
            }

            // 检查是否已加载
            if (loadedResources.ContainsKey(path))
            {
                T cachedResource = loadedResources[path] as T;
                if (cachedResource != null)
                {
                    LogDebug($"从缓存加载资源: {path}");
                    return cachedResource;
                }
            }

            LogDebug($"加载资源: {path}, 模式: {loadMode}");

            T resource = null;

            try
            {
                switch (loadMode)
                {
                    case LoadMode.Local:
                        resource = LoadLocalResource<T>(path);
                        break;
                    case LoadMode.StreamingAssets:
                        resource = LoadStreamingAssetsResource<T>(path);
                        break;
                    case LoadMode.Remote:
                        LogDebug("远程资源需要使用异步加载");
                        break;
                }

                if (resource != null)
                {
                    // 缓存资源
                    loadedResources[path] = resource;
                    
                    // 记录资源信息
                    resourceInfos[path] = new ResourceInfo
                    {
                        path = path,
                        loadMode = loadMode,
                        resourceType = GetResourceType(path),
                        loadTime = DateTime.Now
                    };

                    LogDebug($"资源加载成功: {path}");
                }
                else
                {
                    LogDebug($"资源加载失败: {path}");
                }
            }
            catch (Exception e)
            {
                LogDebug($"加载资源异常: {path}, 错误: {e.Message}");
            }

            return resource;
        }

        public void LoadResourceAsync<T>(string path, Action<T> callback, LoadMode loadMode = LoadMode.Local) where T : UnityEngine.Object
        {
            if (string.IsNullOrEmpty(path))
            {
                LogDebug("资源路径不能为空");
                callback?.Invoke(null);
                return;
            }

            // 检查是否已加载
            if (loadedResources.ContainsKey(path))
            {
                T cachedResource = loadedResources[path] as T;
                if (cachedResource != null)
                {
                    LogDebug($"从缓存加载资源: {path}");
                    callback?.Invoke(cachedResource);
                    return;
                }
            }

            LogDebug($"异步加载资源: {path}, 模式: {loadMode}");
            StartCoroutine(LoadResourceAsyncCoroutine(path, callback, loadMode));
        }

        public void UnloadResource(string path)
        {
            if (string.IsNullOrEmpty(path))
            {
                LogDebug("资源路径不能为空");
                return;
            }

            if (!loadedResources.ContainsKey(path))
            {
                LogDebug($"资源未加载: {path}");
                return;
            }

            LogDebug($"卸载资源: {path}");

            UnityEngine.Object resource = loadedResources[path];
            if (resource != null)
            {
                Resources.UnloadAsset(resource);
            }

            loadedResources.Remove(path);
            resourceInfos.Remove(path);
        }

        public void DownloadResource(string url, string savePath, Action<float> onProgress = null, Action<bool> onComplete = null)
        {
            if (string.IsNullOrEmpty(url) || string.IsNullOrEmpty(savePath))
            {
                LogDebug("下载URL或保存路径不能为空");
                onComplete?.Invoke(false);
                return;
            }

            LogDebug($"添加下载任务: {url} -> {savePath}");

            DownloadTask task = new DownloadTask
            {
                url = url,
                savePath = savePath,
                onProgress = onProgress,
                onComplete = onComplete
            };

            downloadQueue.Enqueue(task);
        }

        /// <summary>
        /// 获取资源信息
        /// </summary>
        public ResourceInfo GetResourceInfo(string path)
        {
            return resourceInfos.ContainsKey(path) ? resourceInfos[path] : null;
        }

        /// <summary>
        /// 获取已加载资源列表
        /// </summary>
        public List<string> GetLoadedResourcePaths()
        {
            return new List<string>(loadedResources.Keys);
        }

        /// <summary>
        /// 加载本地资源
        /// </summary>
        private T LoadLocalResource<T>(string path) where T : UnityEngine.Object
        {
            // 移除Resources/前缀（如果存在）
            string resourcePath = path;
            if (resourcePath.StartsWith("Resources/"))
            {
                resourcePath = resourcePath.Substring("Resources/".Length);
            }

            // 移除文件扩展名
            int lastDotIndex = resourcePath.LastIndexOf('.');
            if (lastDotIndex > 0)
            {
                resourcePath = resourcePath.Substring(0, lastDotIndex);
            }

            return Resources.Load<T>(resourcePath);
        }

        /// <summary>
        /// 加载StreamingAssets资源
        /// </summary>
        private T LoadStreamingAssetsResource<T>(string path) where T : UnityEngine.Object
        {
            string fullPath = Path.Combine(Application.streamingAssetsPath, path);
            
            if (typeof(T) == typeof(Texture2D))
            {
                if (File.Exists(fullPath))
                {
                    byte[] data = File.ReadAllBytes(fullPath);
                    Texture2D texture = new Texture2D(2, 2);
                    texture.LoadImage(data);
                    return texture as T;
                }
            }
            else if (typeof(T) == typeof(AudioClip))
            {
                // StreamingAssets中的音频需要使用UnityWebRequest加载
                LogDebug("StreamingAssets音频资源需要使用异步加载");
            }

            return null;
        }

        /// <summary>
        /// 异步加载资源协程
        /// </summary>
        private IEnumerator LoadResourceAsyncCoroutine<T>(string path, Action<T> callback, LoadMode loadMode) where T : UnityEngine.Object
        {
            T resource = null;

            switch (loadMode)
            {
                case LoadMode.Local:
                    ResourceRequest request = Resources.LoadAsync<T>(GetResourcePath(path));
                    yield return request;
                    resource = request.asset as T;
                    break;

                case LoadMode.StreamingAssets:
                    yield return StartCoroutine(LoadStreamingAssetsAsyncCoroutine<T>(path, (result) => resource = result));
                    break;

                case LoadMode.Remote:
                    yield return StartCoroutine(LoadRemoteResourceCoroutine<T>(path, (result) => resource = result));
                    break;
            }

            if (resource != null)
            {
                // 缓存资源
                loadedResources[path] = resource;
                
                // 记录资源信息
                resourceInfos[path] = new ResourceInfo
                {
                    path = path,
                    loadMode = loadMode,
                    resourceType = GetResourceType(path),
                    loadTime = DateTime.Now
                };

                LogDebug($"异步资源加载成功: {path}");
            }
            else
            {
                LogDebug($"异步资源加载失败: {path}");
            }

            callback?.Invoke(resource);
        }

        /// <summary>
        /// 异步加载StreamingAssets资源
        /// </summary>
        private IEnumerator LoadStreamingAssetsAsyncCoroutine<T>(string path, Action<T> callback) where T : UnityEngine.Object
        {
            string fullPath = Path.Combine(Application.streamingAssetsPath, path);
            
            using (UnityWebRequest request = UnityWebRequest.Get(fullPath))
            {
                yield return request.SendWebRequest();

                if (request.result == UnityWebRequest.Result.Success)
                {
                    if (typeof(T) == typeof(Texture2D))
                    {
                        Texture2D texture = new Texture2D(2, 2);
                        texture.LoadImage(request.downloadHandler.data);
                        callback?.Invoke(texture as T);
                    }
                    else if (typeof(T) == typeof(AudioClip))
                    {
                        AudioClip clip = DownloadHandlerAudioClip.GetContent(request);
                        callback?.Invoke(clip as T);
                    }
                    else
                    {
                        callback?.Invoke(null);
                    }
                }
                else
                {
                    LogDebug($"StreamingAssets资源加载失败: {path}, 错误: {request.error}");
                    callback?.Invoke(null);
                }
            }
        }

        /// <summary>
        /// 加载远程资源协程
        /// </summary>
        private IEnumerator LoadRemoteResourceCoroutine<T>(string url, Action<T> callback) where T : UnityEngine.Object
        {
            using (UnityWebRequest request = UnityWebRequest.Get(url))
            {
                yield return request.SendWebRequest();

                if (request.result == UnityWebRequest.Result.Success)
                {
                    if (typeof(T) == typeof(Texture2D))
                    {
                        Texture2D texture = DownloadHandlerTexture.GetContent(request);
                        callback?.Invoke(texture as T);
                    }
                    else if (typeof(T) == typeof(AudioClip))
                    {
                        AudioClip clip = DownloadHandlerAudioClip.GetContent(request);
                        callback?.Invoke(clip as T);
                    }
                    else
                    {
                        callback?.Invoke(null);
                    }
                }
                else
                {
                    LogDebug($"远程资源加载失败: {url}, 错误: {request.error}");
                    callback?.Invoke(null);
                }
            }
        }

        /// <summary>
        /// 处理下载队列
        /// </summary>
        private void ProcessDownloadQueue()
        {
            // 清理已完成的下载
            activeDownloads.RemoveAll(task => task.isCompleted);

            // 启动新的下载任务
            while (downloadQueue.Count > 0 && activeDownloads.Count < maxConcurrentDownloads)
            {
                DownloadTask task = downloadQueue.Dequeue();
                activeDownloads.Add(task);
                StartCoroutine(DownloadResourceCoroutine(task));
            }
        }

        /// <summary>
        /// 下载资源协程
        /// </summary>
        private IEnumerator DownloadResourceCoroutine(DownloadTask task)
        {
            LogDebug($"开始下载: {task.url}");

            using (UnityWebRequest request = UnityWebRequest.Get(task.url))
            {
                request.downloadHandler = new DownloadHandlerFile(task.savePath);

                var operation = request.SendWebRequest();

                while (!operation.isDone)
                {
                    task.onProgress?.Invoke(request.downloadProgress);
                    yield return null;
                }

                if (request.result == UnityWebRequest.Result.Success)
                {
                    LogDebug($"下载完成: {task.url} -> {task.savePath}");
                    task.onComplete?.Invoke(true);
                }
                else
                {
                    LogDebug($"下载失败: {task.url}, 错误: {request.error}");
                    task.onComplete?.Invoke(false);
                }
            }

            task.isCompleted = true;
        }

        /// <summary>
        /// 停止所有下载
        /// </summary>
        private void StopAllDownloads()
        {
            downloadQueue.Clear();
            activeDownloads.Clear();
            StopAllCoroutines();
        }

        /// <summary>
        /// 卸载所有资源
        /// </summary>
        private void UnloadAllResources()
        {
            foreach (var resource in loadedResources.Values)
            {
                if (resource != null)
                {
                    Resources.UnloadAsset(resource);
                }
            }

            loadedResources.Clear();
            resourceInfos.Clear();
            Resources.UnloadUnusedAssets();
        }

        /// <summary>
        /// 获取资源路径（移除前缀和扩展名）
        /// </summary>
        private string GetResourcePath(string path)
        {
            string resourcePath = path;
            
            if (resourcePath.StartsWith("Resources/"))
            {
                resourcePath = resourcePath.Substring("Resources/".Length);
            }

            int lastDotIndex = resourcePath.LastIndexOf('.');
            if (lastDotIndex > 0)
            {
                resourcePath = resourcePath.Substring(0, lastDotIndex);
            }

            return resourcePath;
        }

        /// <summary>
        /// 获取资源类型
        /// </summary>
        private ResourceType GetResourceType(string path)
        {
            string extension = Path.GetExtension(path).ToLower();
            
            return extension switch
            {
                ".prefab" => ResourceType.Prefab,
                ".unity" => ResourceType.Scene,
                ".png" or ".jpg" or ".jpeg" or ".tga" or ".bmp" => ResourceType.Texture,
                ".wav" or ".mp3" or ".ogg" or ".aiff" => ResourceType.Audio,
                ".mat" => ResourceType.Material,
                ".anim" => ResourceType.Animation,
                ".ttf" or ".otf" => ResourceType.Font,
                ".shader" => ResourceType.Shader,
                ".json" or ".xml" or ".txt" => ResourceType.Config,
                ".lua" => ResourceType.Lua,
                _ => ResourceType.None
            };
        }

        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[ResourceManager] {message}");
            }
        }
    }

    /// <summary>
    /// 资源信息类
    /// </summary>
    [Serializable]
    public class ResourceInfo
    {
        public string path;
        public LoadMode loadMode;
        public ResourceType resourceType;
        public DateTime loadTime;
    }

    /// <summary>
    /// 下载任务类
    /// </summary>
    public class DownloadTask
    {
        public string url;
        public string savePath;
        public Action<float> onProgress;
        public Action<bool> onComplete;
        public bool isCompleted;
    }
}
