using UnityEngine;
using GameFramework.Core;
using GameFramework.XLua;

namespace GameFramework.Examples
{
    /// <summary>
    /// Lua使用示例
    /// 展示如何在Unity中使用Lua脚本
    /// </summary>
    public class LuaExample : MonoBehaviour
    {
        [Header("Lua示例设置")]
        [SerializeField] private bool enableDebugLog = true;

        private LuaManager luaManager;

        private void Start()
        {
            // 等待框架初始化
            if (GameFramework.Core.GameFramework.Instance.IsInitialized)
            {
                OnFrameworkReady();
            }
            else
            {
                GameFramework.Core.GameFramework.OnFrameworkInitialized += OnFrameworkReady;
            }
        }

        private void OnFrameworkReady()
        {
            LogDebug("框架初始化完成，开始Lua示例");

            // 创建Lua管理器
            SetupLuaManager();

            // 演示基本Lua功能
            DemoBasicLuaFunctions();

            // 演示Lua事件系统
            DemoLuaEventSystem();

            // 演示Lua协程
            DemoLuaCoroutines();

            // 演示Lua与游戏框架交互
            DemoLuaFrameworkIntegration();
        }

        /// <summary>
        /// 设置Lua管理器
        /// </summary>
        private void SetupLuaManager()
        {
            // 查找或创建Lua管理器
            luaManager = FindObjectOfType<LuaManager>();
            if (luaManager == null)
            {
                GameObject luaManagerObject = new GameObject("LuaManager");
                luaManager = luaManagerObject.AddComponent<LuaManager>();
            }

            LogDebug("Lua管理器设置完成");
        }

        /// <summary>
        /// 演示基本Lua功能
        /// </summary>
        private void DemoBasicLuaFunctions()
        {
            LogDebug("=== 演示基本Lua功能 ===");

            // 执行简单的Lua代码
            luaManager.DoString("print('Hello from Lua!')");

            // 设置和获取Lua全局变量
            luaManager.SetLuaGlobal("testNumber", 42);
            luaManager.SetLuaGlobal("testString", "Hello Unity");

            int number = luaManager.GetLuaGlobal<int>("testNumber");
            string text = luaManager.GetLuaGlobal<string>("testString");

            LogDebug($"从Lua获取的数值: {number}");
            LogDebug($"从Lua获取的字符串: {text}");

            // 执行Lua函数
            luaManager.DoString(@"
                function Add(a, b)
                    return a + b
                end
                
                function Greet(name)
                    return 'Hello, ' .. name .. '!'
                end
            ");

            var addResult = luaManager.CallLuaFunction("Add", 10, 20);
            var greetResult = luaManager.CallLuaFunction("Greet", "Unity Developer");

            if (addResult != null && addResult.Length > 0)
            {
                LogDebug($"Lua加法结果: {addResult[0]}");
            }

            if (greetResult != null && greetResult.Length > 0)
            {
                LogDebug($"Lua问候结果: {greetResult[0]}");
            }
        }

        /// <summary>
        /// 演示Lua事件系统
        /// </summary>
        private void DemoLuaEventSystem()
        {
            LogDebug("=== 演示Lua事件系统 ===");

            // 注册事件监听器
            LuaGameFramework.Event.AddSimpleListener("TestEvent", () =>
            {
                LogDebug("收到TestEvent事件");
            });

            LuaGameFramework.Event.AddListener("TestEventWithData", (data) =>
            {
                LogDebug($"收到TestEventWithData事件，数据: {data}");
            });

            // 触发事件
            LuaGameFramework.Event.TriggerSimpleEvent("TestEvent");
            LuaGameFramework.Event.TriggerEvent("TestEventWithData", "这是测试数据");

            // 延迟触发事件
            LuaGameFramework.Event.TriggerSimpleEventDelayed("TestEvent", 2f);
        }

        /// <summary>
        /// 演示Lua协程
        /// </summary>
        private void DemoLuaCoroutines()
        {
            LogDebug("=== 演示Lua协程 ===");

            // 延迟执行
            LuaCoroutineHelper.Delay(1f, () =>
            {
                LogDebug("延迟1秒执行的动作");
            });

            // 重复执行
            int repeatId = LuaCoroutineHelper.Repeat(0.5f, () =>
            {
                LogDebug("每0.5秒重复执行的动作");
            }, 5); // 重复5次

            // 渐变动画
            LuaCoroutineHelper.Tween(3f, (progress) =>
            {
                LogDebug($"渐变进度: {progress:P}");
            }, () =>
            {
                LogDebug("渐变动画完成");
            });

            // 创建定时器
            int timerId = LuaCoroutineHelper.CreateTimer(1f, () =>
            {
                LogDebug("定时器触发");
            }, 3); // 重复3次

            // 5秒后销毁定时器
            LuaCoroutineHelper.Delay(5f, () =>
            {
                LuaCoroutineHelper.DestroyTimer(timerId);
                LogDebug("定时器已销毁");
            });
        }

        /// <summary>
        /// 演示Lua与游戏框架交互
        /// </summary>
        private void DemoLuaFrameworkIntegration()
        {
            LogDebug("=== 演示Lua与游戏框架交互 ===");

            // 演示游戏状态管理
            LogDebug($"当前游戏状态: {LuaGameFramework.GameState.GetCurrentStateName()}");

            // 注册状态改变事件
            LuaGameFramework.GameState.OnStateChanged((oldState, newState) =>
            {
                LogDebug($"游戏状态改变: {oldState} -> {newState}");
            });

            // 演示输入系统
            if (LuaGameFramework.Input.InputEnabled)
            {
                LogDebug($"当前输入设备: {LuaGameFramework.Input.GetDeviceTypeName(LuaGameFramework.Input.CurrentDeviceType)}");
            }

            // 演示资源加载
            LuaResourceHelper.LoadConfig("TestConfig", (content) =>
            {
                if (!string.IsNullOrEmpty(content))
                {
                    LogDebug($"加载配置文件成功，内容长度: {content.Length}");
                }
                else
                {
                    LogDebug("配置文件不存在或为空");
                }
            });

            // 演示UI管理
            LogDebug("尝试创建测试UI");
            // 注意：这需要对应的UI预制体存在
            // GameObject testUI = LuaGameFramework.UI.CreateUI("TestUI", 2, 3); // Popup, Popup layer
        }

        /// <summary>
        /// 演示Lua脚本文件加载
        /// </summary>
        private void DemoLuaScriptLoading()
        {
            LogDebug("=== 演示Lua脚本文件加载 ===");

            // 创建示例Lua脚本内容
            string exampleLuaScript = @"
-- 示例Lua脚本
local ExampleScript = {}

function ExampleScript.Initialize()
    print('Lua脚本初始化')
    
    -- 注册事件监听器
    Event.AddSimpleListener('LuaScriptTest', function()
        print('Lua脚本收到测试事件')
    end)
    
    -- 创建定时器
    local timerId = Coroutine.CreateTimer(2, function()
        print('Lua定时器触发: ' .. Time.time)
    end, 3)
    
    print('Lua脚本初始化完成')
end

function ExampleScript.Update()
    -- 检查输入
    if Input.GetJumpInput() then
        print('Lua检测到跳跃输入')
        Event.TriggerSimpleEvent('LuaJumpEvent')
    end
end

function ExampleScript.OnDestroy()
    print('Lua脚本销毁')
end

-- 自动初始化
ExampleScript.Initialize()

return ExampleScript
";

            // 执行示例脚本
            luaManager.DoString(exampleLuaScript);

            // 触发测试事件
            LuaCoroutineHelper.Delay(1f, () =>
            {
                LuaGameFramework.Event.TriggerSimpleEvent("LuaScriptTest");
            });
        }

        private void Update()
        {
            // 演示实时输入检测
            if (Input.GetKeyDown(KeyCode.L))
            {
                DemoLuaScriptLoading();
            }
            else if (Input.GetKeyDown(KeyCode.R))
            {
                // 重新加载Lua脚本
                if (luaManager != null)
                {
                    luaManager.ReloadLuaScripts();
                    LogDebug("Lua脚本已重新加载");
                }
            }
            else if (Input.GetKeyDown(KeyCode.E))
            {
                // 触发测试事件
                LuaGameFramework.Event.TriggerEvent("TestEventFromUpdate", Time.time);
            }
        }

        private void OnDestroy()
        {
            // 清理事件
            GameFramework.Core.GameFramework.OnFrameworkInitialized -= OnFrameworkReady;
        }

        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[LuaExample] {message}");
            }
        }

        private void OnGUI()
        {
            // 显示Lua示例控制界面
            GUILayout.BeginArea(new Rect(10, 550, 400, 200));
            GUILayout.Label("Lua示例控制");

            if (luaManager != null && luaManager.IsInitialized)
            {
                GUILayout.Label($"Lua环境状态: 已初始化");
                
                if (GUILayout.Button("重新加载Lua脚本"))
                {
                    luaManager.ReloadLuaScripts();
                }
                
                if (GUILayout.Button("执行测试Lua代码"))
                {
                    luaManager.DoString("print('GUI按钮触发的Lua代码: ' .. Time.time)");
                }
                
                if (GUILayout.Button("触发Lua事件"))
                {
                    LuaGameFramework.Event.TriggerEvent("GUITestEvent", "来自GUI的数据");
                }
            }
            else
            {
                GUILayout.Label("Lua环境状态: 未初始化");
            }

            GUILayout.Space(10);
            GUILayout.Label("快捷键:");
            GUILayout.Label("L - 加载示例Lua脚本");
            GUILayout.Label("R - 重新加载Lua脚本");
            GUILayout.Label("E - 触发测试事件");

            GUILayout.EndArea();
        }
    }
}
