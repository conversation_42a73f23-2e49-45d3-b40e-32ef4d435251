using System;
using UnityEngine;

namespace GameFramework.Core
{
    /// <summary>
    /// 管理器基础接口
    /// </summary>
    public interface IManager
    {
        /// <summary>
        /// 管理器名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// 初始化管理器
        /// </summary>
        void Initialize();

        /// <summary>
        /// 更新管理器
        /// </summary>
        void Update();

        /// <summary>
        /// 销毁管理器
        /// </summary>
        void Destroy();
    }

    /// <summary>
    /// 游戏状态管理器接口
    /// </summary>
    public interface IGameStateManager : IManager
    {
        /// <summary>
        /// 当前游戏状态
        /// </summary>
        GameState CurrentState { get; }

        /// <summary>
        /// 上一个游戏状态
        /// </summary>
        GameState PreviousState { get; }

        /// <summary>
        /// 游戏状态改变事件
        /// </summary>
        event Action<GameState, GameState> OnStateChanged;

        /// <summary>
        /// 切换游戏状态
        /// </summary>
        /// <param name="newState">新状态</param>
        void ChangeState(GameState newState);

        /// <summary>
        /// 返回上一个状态
        /// </summary>
        void ReturnToPreviousState();

        /// <summary>
        /// 注册状态处理器
        /// </summary>
        /// <param name="state">游戏状态</param>
        /// <param name="handler">状态处理器</param>
        void RegisterStateHandler(GameState state, IGameState handler);

        /// <summary>
        /// 注销状态处理器
        /// </summary>
        /// <param name="state">游戏状态</param>
        void UnregisterStateHandler(GameState state);
    }

    /// <summary>
    /// 场景管理器接口
    /// </summary>
    public interface ISceneManager : IManager
    {
        /// <summary>
        /// 当前场景名称
        /// </summary>
        string CurrentSceneName { get; }

        /// <summary>
        /// 场景加载进度事件
        /// </summary>
        event Action<float> OnSceneLoadProgress;

        /// <summary>
        /// 场景加载完成事件
        /// </summary>
        event Action<string> OnSceneLoaded;

        /// <summary>
        /// 加载场景
        /// </summary>
        /// <param name="sceneName">场景名称</param>
        /// <param name="loadMode">加载模式</param>
        void LoadScene(string sceneName, SceneLoadMode loadMode = SceneLoadMode.Single);

        /// <summary>
        /// 异步加载场景
        /// </summary>
        /// <param name="sceneName">场景名称</param>
        /// <param name="loadMode">加载模式</param>
        void LoadSceneAsync(string sceneName, SceneLoadMode loadMode = SceneLoadMode.Single);

        /// <summary>
        /// 卸载场景
        /// </summary>
        /// <param name="sceneName">场景名称</param>
        void UnloadScene(string sceneName);
    }

    /// <summary>
    /// UI管理器接口
    /// </summary>
    public interface IUIManager : IManager
    {
        /// <summary>
        /// UI根节点
        /// </summary>
        Transform UIRoot { get; }

        /// <summary>
        /// 创建UI
        /// </summary>
        /// <typeparam name="T">UI类型</typeparam>
        /// <param name="uiName">UI名称</param>
        /// <param name="uiType">UI类型</param>
        /// <param name="layer">UI层级</param>
        /// <returns>UI实例</returns>
        T CreateUI<T>(string uiName, UIType uiType = UIType.FullScreen, UILayer layer = UILayer.Normal) where T : MonoBehaviour;

        /// <summary>
        /// 显示UI
        /// </summary>
        /// <param name="uiName">UI名称</param>
        void ShowUI(string uiName);

        /// <summary>
        /// 隐藏UI
        /// </summary>
        /// <param name="uiName">UI名称</param>
        void HideUI(string uiName);

        /// <summary>
        /// 销毁UI
        /// </summary>
        /// <param name="uiName">UI名称</param>
        void DestroyUI(string uiName);

        /// <summary>
        /// 获取UI
        /// </summary>
        /// <typeparam name="T">UI类型</typeparam>
        /// <param name="uiName">UI名称</param>
        /// <returns>UI实例</returns>
        T GetUI<T>(string uiName) where T : MonoBehaviour;
    }

    /// <summary>
    /// 资源管理器接口
    /// </summary>
    public interface IResourceManager : IManager
    {
        /// <summary>
        /// 加载资源
        /// </summary>
        /// <typeparam name="T">资源类型</typeparam>
        /// <param name="path">资源路径</param>
        /// <param name="loadMode">加载模式</param>
        /// <returns>资源实例</returns>
        T LoadResource<T>(string path, LoadMode loadMode = LoadMode.Local) where T : UnityEngine.Object;

        /// <summary>
        /// 异步加载资源
        /// </summary>
        /// <typeparam name="T">资源类型</typeparam>
        /// <param name="path">资源路径</param>
        /// <param name="callback">加载完成回调</param>
        /// <param name="loadMode">加载模式</param>
        void LoadResourceAsync<T>(string path, Action<T> callback, LoadMode loadMode = LoadMode.Local) where T : UnityEngine.Object;

        /// <summary>
        /// 卸载资源
        /// </summary>
        /// <param name="path">资源路径</param>
        void UnloadResource(string path);

        /// <summary>
        /// 下载远程资源
        /// </summary>
        /// <param name="url">下载地址</param>
        /// <param name="savePath">保存路径</param>
        /// <param name="onProgress">下载进度回调</param>
        /// <param name="onComplete">下载完成回调</param>
        void DownloadResource(string url, string savePath, Action<float> onProgress = null, Action<bool> onComplete = null);
    }

    /// <summary>
    /// 输入管理器接口
    /// </summary>
    public interface IInputManager : IManager
    {
        /// <summary>
        /// 当前输入设备类型
        /// </summary>
        InputDeviceType CurrentDeviceType { get; }

        /// <summary>
        /// 是否启用输入
        /// </summary>
        bool InputEnabled { get; set; }

        /// <summary>
        /// 输入动作事件
        /// </summary>
        event Action<InputActionType, InputState, object> OnInputAction;

        /// <summary>
        /// 设备切换事件
        /// </summary>
        event Action<InputDeviceType> OnDeviceChanged;

        /// <summary>
        /// 获取按钮输入状态
        /// </summary>
        /// <param name="actionType">动作类型</param>
        /// <returns>是否按下</returns>
        bool GetButton(InputActionType actionType);

        /// <summary>
        /// 获取按钮按下事件
        /// </summary>
        /// <param name="actionType">动作类型</param>
        /// <returns>是否刚按下</returns>
        bool GetButtonDown(InputActionType actionType);

        /// <summary>
        /// 获取按钮释放事件
        /// </summary>
        /// <param name="actionType">动作类型</param>
        /// <returns>是否刚释放</returns>
        bool GetButtonUp(InputActionType actionType);

        /// <summary>
        /// 获取轴输入值
        /// </summary>
        /// <param name="actionType">动作类型</param>
        /// <returns>轴值</returns>
        float GetAxis(InputActionType actionType);

        /// <summary>
        /// 获取向量输入值
        /// </summary>
        /// <param name="actionType">动作类型</param>
        /// <returns>向量值</returns>
        Vector2 GetVector2(InputActionType actionType);

        /// <summary>
        /// 启用输入动作映射
        /// </summary>
        /// <param name="mapName">映射名称</param>
        void EnableActionMap(string mapName);

        /// <summary>
        /// 禁用输入动作映射
        /// </summary>
        /// <param name="mapName">映射名称</param>
        void DisableActionMap(string mapName);

        /// <summary>
        /// 切换输入动作映射
        /// </summary>
        /// <param name="mapName">映射名称</param>
        void SwitchActionMap(string mapName);
    }
}
