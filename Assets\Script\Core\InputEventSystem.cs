using System;
using System.Collections.Generic;
using UnityEngine;

namespace GameFramework.Core
{
    /// <summary>
    /// 输入事件系统
    /// 提供更高级的输入事件处理和绑定功能
    /// </summary>
    public class InputEventSystem : MonoBehaviour
    {
        [Header("输入事件系统设置")]
        [SerializeField] private bool enableDebugLog = true;

        private Dictionary<InputActionType, List<InputEventHandler>> eventHandlers = new Dictionary<InputActionType, List<InputEventHandler>>();
        private Dictionary<string, InputEventGroup> eventGroups = new Dictionary<string, InputEventGroup>();

        public bool IsInitialized { get; private set; }

        private void Awake()
        {
            Initialize();
        }

        private void Start()
        {
            // 注册到输入管理器
            if (GameFramework.Input != null)
            {
                GameFramework.Input.OnInputAction += HandleInputAction;
            }
        }

        private void OnDestroy()
        {
            // 注销事件
            if (GameFramework.Input != null)
            {
                GameFramework.Input.OnInputAction -= HandleInputAction;
            }
        }

        /// <summary>
        /// 初始化
        /// </summary>
        public void Initialize()
        {
            if (IsInitialized)
                return;

            LogDebug("初始化输入事件系统");
            IsInitialized = true;
        }

        /// <summary>
        /// 绑定输入事件
        /// </summary>
        /// <param name="actionType">动作类型</param>
        /// <param name="handler">事件处理器</param>
        /// <param name="priority">优先级（数值越小优先级越高）</param>
        public void BindInputEvent(InputActionType actionType, Action<InputActionType, InputState, object> handler, int priority = 0)
        {
            if (!eventHandlers.ContainsKey(actionType))
            {
                eventHandlers[actionType] = new List<InputEventHandler>();
            }

            var eventHandler = new InputEventHandler
            {
                handler = handler,
                priority = priority,
                isEnabled = true
            };

            eventHandlers[actionType].Add(eventHandler);
            
            // 按优先级排序
            eventHandlers[actionType].Sort((a, b) => a.priority.CompareTo(b.priority));

            LogDebug($"绑定输入事件: {actionType}, 优先级: {priority}");
        }

        /// <summary>
        /// 解绑输入事件
        /// </summary>
        /// <param name="actionType">动作类型</param>
        /// <param name="handler">事件处理器</param>
        public void UnbindInputEvent(InputActionType actionType, Action<InputActionType, InputState, object> handler)
        {
            if (eventHandlers.ContainsKey(actionType))
            {
                eventHandlers[actionType].RemoveAll(h => h.handler == handler);
                LogDebug($"解绑输入事件: {actionType}");
            }
        }

        /// <summary>
        /// 创建输入事件组
        /// </summary>
        /// <param name="groupName">组名</param>
        /// <param name="enabled">是否启用</param>
        public void CreateEventGroup(string groupName, bool enabled = true)
        {
            if (!eventGroups.ContainsKey(groupName))
            {
                eventGroups[groupName] = new InputEventGroup
                {
                    name = groupName,
                    isEnabled = enabled,
                    handlers = new List<InputEventHandler>()
                };
                LogDebug($"创建输入事件组: {groupName}");
            }
        }

        /// <summary>
        /// 启用事件组
        /// </summary>
        /// <param name="groupName">组名</param>
        public void EnableEventGroup(string groupName)
        {
            if (eventGroups.ContainsKey(groupName))
            {
                eventGroups[groupName].isEnabled = true;
                LogDebug($"启用事件组: {groupName}");
            }
        }

        /// <summary>
        /// 禁用事件组
        /// </summary>
        /// <param name="groupName">组名</param>
        public void DisableEventGroup(string groupName)
        {
            if (eventGroups.ContainsKey(groupName))
            {
                eventGroups[groupName].isEnabled = false;
                LogDebug($"禁用事件组: {groupName}");
            }
        }

        /// <summary>
        /// 向事件组添加处理器
        /// </summary>
        /// <param name="groupName">组名</param>
        /// <param name="actionType">动作类型</param>
        /// <param name="handler">事件处理器</param>
        /// <param name="priority">优先级</param>
        public void AddHandlerToGroup(string groupName, InputActionType actionType, Action<InputActionType, InputState, object> handler, int priority = 0)
        {
            if (eventGroups.ContainsKey(groupName))
            {
                var eventHandler = new InputEventHandler
                {
                    handler = handler,
                    priority = priority,
                    isEnabled = true,
                    groupName = groupName
                };

                eventGroups[groupName].handlers.Add(eventHandler);
                BindInputEvent(actionType, handler, priority);
            }
        }

        /// <summary>
        /// 处理输入动作
        /// </summary>
        private void HandleInputAction(InputActionType actionType, InputState state, object value)
        {
            if (!eventHandlers.ContainsKey(actionType))
                return;

            foreach (var handler in eventHandlers[actionType])
            {
                if (!handler.isEnabled)
                    continue;

                // 检查事件组是否启用
                if (!string.IsNullOrEmpty(handler.groupName))
                {
                    if (!eventGroups.ContainsKey(handler.groupName) || !eventGroups[handler.groupName].isEnabled)
                        continue;
                }

                try
                {
                    handler.handler?.Invoke(actionType, state, value);
                }
                catch (Exception e)
                {
                    LogDebug($"输入事件处理异常: {actionType}, 错误: {e.Message}");
                }
            }
        }

        /// <summary>
        /// 清理所有事件
        /// </summary>
        public void ClearAllEvents()
        {
            eventHandlers.Clear();
            eventGroups.Clear();
            LogDebug("清理所有输入事件");
        }

        /// <summary>
        /// 获取事件处理器数量
        /// </summary>
        /// <param name="actionType">动作类型</param>
        /// <returns>处理器数量</returns>
        public int GetHandlerCount(InputActionType actionType)
        {
            return eventHandlers.ContainsKey(actionType) ? eventHandlers[actionType].Count : 0;
        }

        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[InputEventSystem] {message}");
            }
        }
    }

    /// <summary>
    /// 输入事件处理器
    /// </summary>
    [Serializable]
    public class InputEventHandler
    {
        public Action<InputActionType, InputState, object> handler;
        public int priority;
        public bool isEnabled;
        public string groupName;
    }

    /// <summary>
    /// 输入事件组
    /// </summary>
    [Serializable]
    public class InputEventGroup
    {
        public string name;
        public bool isEnabled;
        public List<InputEventHandler> handlers;
    }

    /// <summary>
    /// 输入组合键
    /// </summary>
    [Serializable]
    public class InputCombo
    {
        public string name;
        public List<InputActionType> keys;
        public float timeWindow = 1f; // 组合键时间窗口
        public Action onComboTriggered;
        
        [NonSerialized]
        public List<float> keyPressTimes = new List<float>();
    }

    /// <summary>
    /// 输入手势识别器
    /// </summary>
    public class InputGestureRecognizer : MonoBehaviour
    {
        [Header("手势识别设置")]
        [SerializeField] private float gestureThreshold = 50f;
        [SerializeField] private float gestureTimeLimit = 2f;

        private Vector2 gestureStartPos;
        private float gestureStartTime;
        private bool isGesturing = false;

        public event Action<Vector2> OnSwipeLeft;
        public event Action<Vector2> OnSwipeRight;
        public event Action<Vector2> OnSwipeUp;
        public event Action<Vector2> OnSwipeDown;
        public event Action<Vector2, float> OnPinch;
        public event Action<Vector2> OnTap;
        public event Action<Vector2> OnDoubleTap;

        private void Update()
        {
            DetectGestures();
        }

        private void DetectGestures()
        {
            // 触摸手势检测
            if (Touchscreen.current != null)
            {
                DetectTouchGestures();
            }

            // 鼠标手势检测
            if (Mouse.current != null)
            {
                DetectMouseGestures();
            }
        }

        private void DetectTouchGestures()
        {
            var touch = Touchscreen.current.primaryTouch;
            
            if (touch.press.wasPressedThisFrame)
            {
                gestureStartPos = touch.position.ReadValue();
                gestureStartTime = Time.time;
                isGesturing = true;
            }
            else if (touch.press.wasReleasedThisFrame && isGesturing)
            {
                Vector2 endPos = touch.position.ReadValue();
                float gestureTime = Time.time - gestureStartTime;
                
                if (gestureTime <= gestureTimeLimit)
                {
                    Vector2 gestureVector = endPos - gestureStartPos;
                    float gestureDistance = gestureVector.magnitude;
                    
                    if (gestureDistance >= gestureThreshold)
                    {
                        // 滑动手势
                        DetectSwipeDirection(gestureVector);
                    }
                    else
                    {
                        // 点击手势
                        OnTap?.Invoke(gestureStartPos);
                    }
                }
                
                isGesturing = false;
            }
        }

        private void DetectMouseGestures()
        {
            var mouse = Mouse.current;
            
            if (mouse.leftButton.wasPressedThisFrame)
            {
                gestureStartPos = mouse.position.ReadValue();
                gestureStartTime = Time.time;
                isGesturing = true;
            }
            else if (mouse.leftButton.wasReleasedThisFrame && isGesturing)
            {
                Vector2 endPos = mouse.position.ReadValue();
                float gestureTime = Time.time - gestureStartTime;
                
                if (gestureTime <= gestureTimeLimit)
                {
                    Vector2 gestureVector = endPos - gestureStartPos;
                    float gestureDistance = gestureVector.magnitude;
                    
                    if (gestureDistance >= gestureThreshold)
                    {
                        DetectSwipeDirection(gestureVector);
                    }
                    else
                    {
                        OnTap?.Invoke(gestureStartPos);
                    }
                }
                
                isGesturing = false;
            }
        }

        private void DetectSwipeDirection(Vector2 gestureVector)
        {
            Vector2 normalizedGesture = gestureVector.normalized;
            
            if (Mathf.Abs(normalizedGesture.x) > Mathf.Abs(normalizedGesture.y))
            {
                // 水平滑动
                if (normalizedGesture.x > 0)
                    OnSwipeRight?.Invoke(gestureVector);
                else
                    OnSwipeLeft?.Invoke(gestureVector);
            }
            else
            {
                // 垂直滑动
                if (normalizedGesture.y > 0)
                    OnSwipeUp?.Invoke(gestureVector);
                else
                    OnSwipeDown?.Invoke(gestureVector);
            }
        }
    }
}
