using System;
using System.Collections.Generic;
using UnityEngine;

namespace GameFramework.Core
{
    /// <summary>
    /// 游戏状态管理器
    /// 控制游戏从启动到关闭的整个生命周期流程
    /// </summary>
    public class GameStateManager : MonoBehaviour, IGameStateManager
    {
        [Header("游戏状态管理器设置")]
        [SerializeField] private GameState initialState = GameState.Initialize;
        [SerializeField] private bool enableDebugLog = true;

        private GameState currentState = GameState.None;
        private GameState previousState = GameState.None;
        private Dictionary<GameState, IGameState> stateHandlers = new Dictionary<GameState, IGameState>();

        public string Name => "GameStateManager";
        public bool IsInitialized { get; private set; }
        public GameState CurrentState => currentState;
        public GameState PreviousState => previousState;

        public event Action<GameState, GameState> OnStateChanged;

        private void Awake()
        {
            // 确保单例
            if (FindObjectsOfType<GameStateManager>().Length > 1)
            {
                Destroy(gameObject);
                return;
            }
            DontDestroyOnLoad(gameObject);
        }

        private void Start()
        {
            Initialize();
        }

        private void Update()
        {
            if (IsInitialized)
            {
                Update();
                
                // 更新当前状态处理器
                if (stateHandlers.ContainsKey(currentState))
                {
                    stateHandlers[currentState].OnUpdate();
                }
            }
        }

        public void Initialize()
        {
            if (IsInitialized)
                return;

            LogDebug("初始化游戏状态管理器");

            // 注册默认状态处理器
            RegisterDefaultStateHandlers();

            // 设置初始状态
            ChangeState(initialState);

            IsInitialized = true;
            LogDebug("游戏状态管理器初始化完成");
        }

        public void Destroy()
        {
            LogDebug("销毁游戏状态管理器");

            // 退出当前状态
            if (stateHandlers.ContainsKey(currentState))
            {
                stateHandlers[currentState].OnExit();
            }

            // 清理状态处理器
            foreach (var handler in stateHandlers.Values)
            {
                handler.OnDestroy();
            }
            stateHandlers.Clear();

            OnStateChanged = null;
            IsInitialized = false;
        }

        public void ChangeState(GameState newState)
        {
            if (currentState == newState)
                return;

            LogDebug($"状态切换: {currentState} -> {newState}");

            GameState oldState = currentState;

            // 退出当前状态
            if (stateHandlers.ContainsKey(currentState))
            {
                stateHandlers[currentState].OnExit();
            }

            // 更新状态
            previousState = currentState;
            currentState = newState;

            // 进入新状态
            if (stateHandlers.ContainsKey(currentState))
            {
                stateHandlers[currentState].OnEnter();
            }

            // 触发状态改变事件
            OnStateChanged?.Invoke(oldState, newState);
        }

        public void ReturnToPreviousState()
        {
            if (previousState != GameState.None)
            {
                ChangeState(previousState);
            }
        }

        /// <summary>
        /// 注册状态处理器
        /// </summary>
        /// <param name="state">游戏状态</param>
        /// <param name="handler">状态处理器</param>
        public void RegisterStateHandler(GameState state, IGameState handler)
        {
            if (stateHandlers.ContainsKey(state))
            {
                LogDebug($"替换状态处理器: {state}");
                stateHandlers[state].OnDestroy();
            }

            stateHandlers[state] = handler;
            handler.Initialize();
            LogDebug($"注册状态处理器: {state}");
        }

        /// <summary>
        /// 注销状态处理器
        /// </summary>
        /// <param name="state">游戏状态</param>
        public void UnregisterStateHandler(GameState state)
        {
            if (stateHandlers.ContainsKey(state))
            {
                stateHandlers[state].OnDestroy();
                stateHandlers.Remove(state);
                LogDebug($"注销状态处理器: {state}");
            }
        }

        /// <summary>
        /// 注册默认状态处理器
        /// </summary>
        private void RegisterDefaultStateHandlers()
        {
            RegisterStateHandler(GameState.Initialize, new InitializeState());
            RegisterStateHandler(GameState.Loading, new LoadingState());
            RegisterStateHandler(GameState.MainMenu, new MainMenuState());
            RegisterStateHandler(GameState.InGame, new InGameState());
            RegisterStateHandler(GameState.Paused, new PausedState());
            RegisterStateHandler(GameState.GameOver, new GameOverState());
            RegisterStateHandler(GameState.Settings, new SettingsState());
            RegisterStateHandler(GameState.Quit, new QuitState());
        }

        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[GameStateManager] {message}");
            }
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus && currentState == GameState.InGame)
            {
                ChangeState(GameState.Paused);
            }
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && currentState == GameState.InGame)
            {
                ChangeState(GameState.Paused);
            }
        }

        void IManager.Update()
        {
            // 接口实现，避免与MonoBehaviour的Update冲突
        }
    }

    /// <summary>
    /// 游戏状态接口
    /// </summary>
    public interface IGameState
    {
        void Initialize();
        void OnEnter();
        void OnUpdate();
        void OnExit();
        void OnDestroy();
    }
}
