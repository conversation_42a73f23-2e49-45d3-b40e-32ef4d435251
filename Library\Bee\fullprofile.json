{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 23380, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 23380, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 23380, "tid": 124, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 23380, "tid": 124, "ts": 1756458123582306, "dur": 10, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 23380, "tid": 124, "ts": 1756458123582327, "dur": 18, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 23380, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 23380, "tid": 1, "ts": 1756458123187109, "dur": 1182, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 23380, "tid": 1, "ts": 1756458123188294, "dur": 32244, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 23380, "tid": 1, "ts": 1756458123220541, "dur": 74247, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 23380, "tid": 124, "ts": 1756458123582348, "dur": 7, "ph": "X", "name": "", "args": {}}, {"pid": 23380, "tid": 68719476736, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123187074, "dur": 20915, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123207990, "dur": 373871, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123208000, "dur": 42, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123208044, "dur": 18, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123208063, "dur": 1094, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123209165, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123209168, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123209264, "dur": 3, "ph": "X", "name": "ProcessMessages 1938", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123209269, "dur": 92, "ph": "X", "name": "ReadAsync 1938", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123209367, "dur": 2, "ph": "X", "name": "ProcessMessages 1929", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123209371, "dur": 48, "ph": "X", "name": "ReadAsync 1929", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123209427, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123209440, "dur": 76, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123209518, "dur": 1, "ph": "X", "name": "ProcessMessages 1143", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123209520, "dur": 107, "ph": "X", "name": "ReadAsync 1143", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123209632, "dur": 227, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123209863, "dur": 3, "ph": "X", "name": "ProcessMessages 3679", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123209867, "dur": 102, "ph": "X", "name": "ReadAsync 3679", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210042, "dur": 3, "ph": "X", "name": "ProcessMessages 4448", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210119, "dur": 85, "ph": "X", "name": "ReadAsync 4448", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210243, "dur": 3, "ph": "X", "name": "ProcessMessages 5495", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210285, "dur": 157, "ph": "X", "name": "ReadAsync 5495", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210448, "dur": 2, "ph": "X", "name": "ProcessMessages 3627", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210450, "dur": 50, "ph": "X", "name": "ReadAsync 3627", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210502, "dur": 1, "ph": "X", "name": "ProcessMessages 2362", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210504, "dur": 28, "ph": "X", "name": "ReadAsync 2362", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210533, "dur": 5, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210542, "dur": 26, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210570, "dur": 3, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210575, "dur": 35, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210616, "dur": 31, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210653, "dur": 2, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210657, "dur": 40, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210703, "dur": 52, "ph": "X", "name": "ReadAsync 998", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210760, "dur": 3, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210765, "dur": 95, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210861, "dur": 1, "ph": "X", "name": "ProcessMessages 2013", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210863, "dur": 23, "ph": "X", "name": "ReadAsync 2013", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210887, "dur": 1, "ph": "X", "name": "ProcessMessages 1554", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210888, "dur": 33, "ph": "X", "name": "ReadAsync 1554", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210924, "dur": 39, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210970, "dur": 25, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210996, "dur": 1, "ph": "X", "name": "ProcessMessages 1126", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123210998, "dur": 39, "ph": "X", "name": "ReadAsync 1126", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211039, "dur": 55, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211099, "dur": 30, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211143, "dur": 1, "ph": "X", "name": "ProcessMessages 1435", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211144, "dur": 46, "ph": "X", "name": "ReadAsync 1435", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211196, "dur": 17, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211217, "dur": 45, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211305, "dur": 56, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211363, "dur": 7, "ph": "X", "name": "ProcessMessages 2591", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211372, "dur": 32, "ph": "X", "name": "ReadAsync 2591", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211419, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211451, "dur": 19, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211473, "dur": 23, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211499, "dur": 38, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211541, "dur": 2, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211545, "dur": 48, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211598, "dur": 1, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211603, "dur": 43, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211648, "dur": 18, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211667, "dur": 30, "ph": "X", "name": "ReadAsync 51", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211704, "dur": 3, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211709, "dur": 35, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211748, "dur": 17, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211766, "dur": 40, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211811, "dur": 35, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211860, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211862, "dur": 35, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211908, "dur": 36, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211950, "dur": 23, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123211982, "dur": 34, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212034, "dur": 22, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212058, "dur": 17, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212078, "dur": 11, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212091, "dur": 32, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212127, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212153, "dur": 11, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212166, "dur": 21, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212189, "dur": 23, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212217, "dur": 20, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212239, "dur": 57, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212296, "dur": 31, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212329, "dur": 1, "ph": "X", "name": "ProcessMessages 1848", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212330, "dur": 24, "ph": "X", "name": "ReadAsync 1848", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212358, "dur": 3, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212363, "dur": 36, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212402, "dur": 24, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212429, "dur": 24, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212463, "dur": 58, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212524, "dur": 31, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212557, "dur": 1, "ph": "X", "name": "ProcessMessages 1644", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212559, "dur": 23, "ph": "X", "name": "ReadAsync 1644", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212584, "dur": 18, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212606, "dur": 26, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212644, "dur": 23, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212669, "dur": 11, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212684, "dur": 16, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212707, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212732, "dur": 22, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212757, "dur": 29, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212789, "dur": 20, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212812, "dur": 40, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212855, "dur": 27, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212895, "dur": 42, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212942, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212944, "dur": 40, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212985, "dur": 1, "ph": "X", "name": "ProcessMessages 1033", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123212987, "dur": 19, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213011, "dur": 53, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213066, "dur": 54, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213123, "dur": 1, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213125, "dur": 36, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213176, "dur": 38, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213217, "dur": 29, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213248, "dur": 1, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213253, "dur": 30, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213285, "dur": 32, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213320, "dur": 17, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213341, "dur": 20, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213363, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213398, "dur": 58, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213463, "dur": 44, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213514, "dur": 117, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213634, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213667, "dur": 34, "ph": "X", "name": "ReadAsync 1026", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213702, "dur": 2, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213708, "dur": 118, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213827, "dur": 4, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213857, "dur": 38, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213896, "dur": 1, "ph": "X", "name": "ProcessMessages 2737", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213898, "dur": 24, "ph": "X", "name": "ReadAsync 2737", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123213925, "dur": 60, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214018, "dur": 28, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214048, "dur": 1, "ph": "X", "name": "ProcessMessages 1276", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214050, "dur": 23, "ph": "X", "name": "ReadAsync 1276", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214105, "dur": 35, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214142, "dur": 23, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214187, "dur": 23, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214315, "dur": 1, "ph": "X", "name": "ProcessMessages 1486", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214316, "dur": 34, "ph": "X", "name": "ReadAsync 1486", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214354, "dur": 1, "ph": "X", "name": "ProcessMessages 2452", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214356, "dur": 60, "ph": "X", "name": "ReadAsync 2452", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214422, "dur": 1, "ph": "X", "name": "ProcessMessages 1809", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214424, "dur": 29, "ph": "X", "name": "ReadAsync 1809", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214472, "dur": 56, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214532, "dur": 1, "ph": "X", "name": "ProcessMessages 1795", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214534, "dur": 73, "ph": "X", "name": "ReadAsync 1795", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214608, "dur": 1, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214610, "dur": 34, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214647, "dur": 1, "ph": "X", "name": "ProcessMessages 1717", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214649, "dur": 36, "ph": "X", "name": "ReadAsync 1717", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214688, "dur": 29, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214719, "dur": 24, "ph": "X", "name": "ReadAsync 1393", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214744, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214746, "dur": 17, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214814, "dur": 32, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214847, "dur": 1, "ph": "X", "name": "ProcessMessages 1424", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214848, "dur": 50, "ph": "X", "name": "ReadAsync 1424", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214905, "dur": 25, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123214931, "dur": 48, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215007, "dur": 1, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215008, "dur": 18, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215029, "dur": 67, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215097, "dur": 27, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215129, "dur": 3, "ph": "X", "name": "ProcessMessages 1422", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215138, "dur": 51, "ph": "X", "name": "ReadAsync 1422", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215192, "dur": 37, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215234, "dur": 1, "ph": "X", "name": "ProcessMessages 1308", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215235, "dur": 27, "ph": "X", "name": "ReadAsync 1308", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215271, "dur": 20, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215298, "dur": 64, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215383, "dur": 8, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215394, "dur": 54, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215453, "dur": 2, "ph": "X", "name": "ProcessMessages 2133", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215456, "dur": 65, "ph": "X", "name": "ReadAsync 2133", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215527, "dur": 1, "ph": "X", "name": "ProcessMessages 934", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215528, "dur": 44, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215574, "dur": 15, "ph": "X", "name": "ProcessMessages 1089", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215590, "dur": 22, "ph": "X", "name": "ReadAsync 1089", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215645, "dur": 3, "ph": "X", "name": "ProcessMessages 1298", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215650, "dur": 43, "ph": "X", "name": "ReadAsync 1298", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215700, "dur": 1, "ph": "X", "name": "ProcessMessages 1137", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215708, "dur": 42, "ph": "X", "name": "ReadAsync 1137", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215752, "dur": 34, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215787, "dur": 11, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215799, "dur": 37, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215844, "dur": 31, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215877, "dur": 20, "ph": "X", "name": "ReadAsync 1013", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215899, "dur": 33, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215934, "dur": 18, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123215968, "dur": 35, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216011, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216012, "dur": 38, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216057, "dur": 50, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216122, "dur": 1, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216124, "dur": 36, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216163, "dur": 32, "ph": "X", "name": "ReadAsync 1165", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216198, "dur": 48, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216252, "dur": 27, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216281, "dur": 73, "ph": "X", "name": "ReadAsync 1199", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216356, "dur": 39, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216403, "dur": 1, "ph": "X", "name": "ProcessMessages 1480", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216404, "dur": 36, "ph": "X", "name": "ReadAsync 1480", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216447, "dur": 26, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216475, "dur": 47, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216524, "dur": 24, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216549, "dur": 26, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216578, "dur": 32, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216612, "dur": 17, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216637, "dur": 35, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216776, "dur": 49, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216826, "dur": 1, "ph": "X", "name": "ProcessMessages 3533", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216831, "dur": 46, "ph": "X", "name": "ReadAsync 3533", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216880, "dur": 63, "ph": "X", "name": "ReadAsync 1157", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216948, "dur": 44, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216992, "dur": 1, "ph": "X", "name": "ProcessMessages 1314", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123216994, "dur": 42, "ph": "X", "name": "ReadAsync 1314", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217039, "dur": 1, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217041, "dur": 26, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217117, "dur": 43, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217167, "dur": 1, "ph": "X", "name": "ProcessMessages 1618", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217172, "dur": 42, "ph": "X", "name": "ReadAsync 1618", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217215, "dur": 2, "ph": "X", "name": "ProcessMessages 929", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217218, "dur": 36, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217308, "dur": 49, "ph": "X", "name": "ReadAsync 1195", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217368, "dur": 2, "ph": "X", "name": "ProcessMessages 1938", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217371, "dur": 27, "ph": "X", "name": "ReadAsync 1938", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217400, "dur": 23, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217428, "dur": 26, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217457, "dur": 101, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217566, "dur": 30, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217598, "dur": 2, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217601, "dur": 29, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217633, "dur": 2, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217635, "dur": 22, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217661, "dur": 7, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217669, "dur": 82, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217754, "dur": 1, "ph": "X", "name": "ProcessMessages 875", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217756, "dur": 40, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217797, "dur": 1, "ph": "X", "name": "ProcessMessages 2031", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217799, "dur": 13, "ph": "X", "name": "ReadAsync 2031", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217814, "dur": 26, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217841, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217867, "dur": 5, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217872, "dur": 23, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217901, "dur": 70, "ph": "X", "name": "ReadAsync 984", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123217973, "dur": 35, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218010, "dur": 1, "ph": "X", "name": "ProcessMessages 2219", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218015, "dur": 23, "ph": "X", "name": "ReadAsync 2219", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218040, "dur": 33, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218077, "dur": 24, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218149, "dur": 21, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218171, "dur": 1, "ph": "X", "name": "ProcessMessages 1259", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218172, "dur": 13, "ph": "X", "name": "ReadAsync 1259", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218187, "dur": 20, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218220, "dur": 30, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218252, "dur": 1, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218257, "dur": 30, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218294, "dur": 38, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218557, "dur": 74, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218668, "dur": 11, "ph": "X", "name": "ProcessMessages 4047", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218679, "dur": 124, "ph": "X", "name": "ReadAsync 4047", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218813, "dur": 2, "ph": "X", "name": "ProcessMessages 2133", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218819, "dur": 39, "ph": "X", "name": "ReadAsync 2133", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218859, "dur": 1, "ph": "X", "name": "ProcessMessages 2309", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218861, "dur": 20, "ph": "X", "name": "ReadAsync 2309", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218885, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218927, "dur": 29, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218959, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123218961, "dur": 65, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219032, "dur": 48, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219085, "dur": 2, "ph": "X", "name": "ProcessMessages 1093", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219088, "dur": 54, "ph": "X", "name": "ReadAsync 1093", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219145, "dur": 62, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219210, "dur": 102, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219313, "dur": 30, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219345, "dur": 46, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219393, "dur": 1, "ph": "X", "name": "ProcessMessages 1433", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219394, "dur": 15, "ph": "X", "name": "ReadAsync 1433", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219412, "dur": 34, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219451, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219489, "dur": 54, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219544, "dur": 1, "ph": "X", "name": "ProcessMessages 997", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219546, "dur": 75, "ph": "X", "name": "ReadAsync 997", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219624, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219672, "dur": 34, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219708, "dur": 31, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219747, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219771, "dur": 35, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219814, "dur": 5, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219819, "dur": 45, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219870, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219913, "dur": 1, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219916, "dur": 32, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219953, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123219955, "dur": 56, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220018, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220054, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220056, "dur": 23, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220084, "dur": 32, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220119, "dur": 85, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220205, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220210, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220251, "dur": 22, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220277, "dur": 13, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220293, "dur": 84, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220379, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220430, "dur": 37, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220476, "dur": 35, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220513, "dur": 49, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220564, "dur": 56, "ph": "X", "name": "ReadAsync 1016", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220637, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220708, "dur": 1, "ph": "X", "name": "ProcessMessages 1074", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220711, "dur": 60, "ph": "X", "name": "ReadAsync 1074", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220774, "dur": 28, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220809, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220811, "dur": 28, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220843, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220876, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220878, "dur": 26, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220906, "dur": 55, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220964, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123220968, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221024, "dur": 43, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221069, "dur": 10, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221080, "dur": 83, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221166, "dur": 32, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221200, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221202, "dur": 53, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221258, "dur": 54, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221315, "dur": 45, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221363, "dur": 81, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221470, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221506, "dur": 1, "ph": "X", "name": "ProcessMessages 1245", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221508, "dur": 37, "ph": "X", "name": "ReadAsync 1245", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221547, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221634, "dur": 1, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221636, "dur": 26, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221664, "dur": 11, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221677, "dur": 41, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221720, "dur": 35, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221758, "dur": 31, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221797, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221842, "dur": 23, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221871, "dur": 57, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221930, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123221985, "dur": 21, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222016, "dur": 41, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222061, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222087, "dur": 1, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222089, "dur": 72, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222163, "dur": 48, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222212, "dur": 29, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222272, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222273, "dur": 46, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222321, "dur": 31, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222354, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222377, "dur": 20, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222400, "dur": 27, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222464, "dur": 10, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222475, "dur": 72, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222625, "dur": 1, "ph": "X", "name": "ProcessMessages 1426", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222628, "dur": 95, "ph": "X", "name": "ReadAsync 1426", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222735, "dur": 107, "ph": "X", "name": "ReadAsync 1021", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222844, "dur": 37, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222924, "dur": 2, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222927, "dur": 25, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222965, "dur": 27, "ph": "X", "name": "ReadAsync 1531", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123222993, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123223051, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123223128, "dur": 24, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123223168, "dur": 72, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123223280, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123223393, "dur": 67, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123223463, "dur": 54, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123223520, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123223521, "dur": 67, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123223591, "dur": 61, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123223655, "dur": 38, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123223739, "dur": 2, "ph": "X", "name": "ProcessMessages 1041", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123223743, "dur": 51, "ph": "X", "name": "ReadAsync 1041", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123223796, "dur": 72, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123223871, "dur": 50, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123223922, "dur": 56, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123223981, "dur": 34, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224041, "dur": 21, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224064, "dur": 98, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224195, "dur": 61, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224258, "dur": 1, "ph": "X", "name": "ProcessMessages 1644", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224259, "dur": 26, "ph": "X", "name": "ReadAsync 1644", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224287, "dur": 50, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224338, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224379, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224458, "dur": 1, "ph": "X", "name": "ProcessMessages 1074", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224460, "dur": 17, "ph": "X", "name": "ReadAsync 1074", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224500, "dur": 31, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224533, "dur": 64, "ph": "X", "name": "ReadAsync 995", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224624, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224634, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224718, "dur": 1, "ph": "X", "name": "ProcessMessages 1567", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224720, "dur": 38, "ph": "X", "name": "ReadAsync 1567", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224759, "dur": 1, "ph": "X", "name": "ProcessMessages 906", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224761, "dur": 23, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224788, "dur": 81, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224935, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224980, "dur": 1, "ph": "X", "name": "ProcessMessages 1742", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123224981, "dur": 37, "ph": "X", "name": "ReadAsync 1742", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225020, "dur": 31, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225052, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225071, "dur": 22, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225095, "dur": 103, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225200, "dur": 66, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225267, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225269, "dur": 65, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225364, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225366, "dur": 35, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225402, "dur": 1, "ph": "X", "name": "ProcessMessages 1210", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225458, "dur": 25, "ph": "X", "name": "ReadAsync 1210", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225485, "dur": 13, "ph": "X", "name": "ReadAsync 1192", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225500, "dur": 61, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225563, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225648, "dur": 38, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225741, "dur": 41, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225805, "dur": 1, "ph": "X", "name": "ProcessMessages 930", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225808, "dur": 24, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123225882, "dur": 146, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226053, "dur": 1, "ph": "X", "name": "ProcessMessages 1153", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226055, "dur": 37, "ph": "X", "name": "ReadAsync 1153", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226093, "dur": 1, "ph": "X", "name": "ProcessMessages 1335", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226094, "dur": 74, "ph": "X", "name": "ReadAsync 1335", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226170, "dur": 2, "ph": "X", "name": "ProcessMessages 934", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226173, "dur": 57, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226293, "dur": 1, "ph": "X", "name": "ProcessMessages 105", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226295, "dur": 32, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226332, "dur": 1, "ph": "X", "name": "ProcessMessages 1743", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226334, "dur": 27, "ph": "X", "name": "ReadAsync 1743", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226364, "dur": 64, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226430, "dur": 20, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226452, "dur": 80, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226533, "dur": 20, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226595, "dur": 73, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226669, "dur": 2, "ph": "X", "name": "ProcessMessages 1085", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226671, "dur": 94, "ph": "X", "name": "ReadAsync 1085", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226768, "dur": 88, "ph": "X", "name": "ReadAsync 1262", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226857, "dur": 60, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226958, "dur": 1, "ph": "X", "name": "ProcessMessages 984", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123226960, "dur": 64, "ph": "X", "name": "ReadAsync 984", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227026, "dur": 23, "ph": "X", "name": "ReadAsync 1225", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227071, "dur": 2, "ph": "X", "name": "ProcessMessages 1237", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227074, "dur": 25, "ph": "X", "name": "ReadAsync 1237", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227101, "dur": 109, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227215, "dur": 58, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227274, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227276, "dur": 26, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227304, "dur": 13, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227319, "dur": 80, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227432, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227495, "dur": 1, "ph": "X", "name": "ProcessMessages 1137", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227497, "dur": 57, "ph": "X", "name": "ReadAsync 1137", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227556, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227637, "dur": 1, "ph": "X", "name": "ProcessMessages 1234", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227639, "dur": 27, "ph": "X", "name": "ReadAsync 1234", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227670, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227774, "dur": 57, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227833, "dur": 22, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227855, "dur": 1, "ph": "X", "name": "ProcessMessages 1169", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227856, "dur": 34, "ph": "X", "name": "ReadAsync 1169", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227892, "dur": 25, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123227982, "dur": 18, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228002, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228003, "dur": 14, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228026, "dur": 21, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228048, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228050, "dur": 33, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228086, "dur": 32, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228120, "dur": 23, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228201, "dur": 56, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228260, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228262, "dur": 35, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228299, "dur": 52, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228429, "dur": 1, "ph": "X", "name": "ProcessMessages 915", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228431, "dur": 35, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228467, "dur": 1, "ph": "X", "name": "ProcessMessages 1757", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228469, "dur": 19, "ph": "X", "name": "ReadAsync 1757", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228490, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228549, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228552, "dur": 107, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228662, "dur": 143, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228835, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228837, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228865, "dur": 29, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228899, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228934, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228966, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123228994, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229022, "dur": 18, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229042, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229043, "dur": 30, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229077, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229125, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229127, "dur": 53, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229183, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229271, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229275, "dur": 92, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229369, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229374, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229404, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229406, "dur": 17, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229466, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229511, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229513, "dur": 43, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229560, "dur": 64, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229875, "dur": 48, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229925, "dur": 6, "ph": "X", "name": "ProcessMessages 1617", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229932, "dur": 26, "ph": "X", "name": "ReadAsync 1617", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229961, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229962, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123229999, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230000, "dur": 24, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230031, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230054, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230056, "dur": 24, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230085, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230086, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230116, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230175, "dur": 32, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230208, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230210, "dur": 71, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230284, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230287, "dur": 38, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230327, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230329, "dur": 32, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230370, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230372, "dur": 37, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230412, "dur": 43, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230458, "dur": 15, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230474, "dur": 33, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230509, "dur": 18, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230554, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230556, "dur": 49, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230607, "dur": 50, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230660, "dur": 19, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230696, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230698, "dur": 21, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230721, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230747, "dur": 36, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230785, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230786, "dur": 43, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230832, "dur": 56, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230891, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230937, "dur": 24, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230987, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123230989, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123231070, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123231119, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123231143, "dur": 8366, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123239645, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123239650, "dur": 452, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123240108, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123240111, "dur": 1002, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123241318, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123241320, "dur": 181, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123241505, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123241582, "dur": 240, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123241826, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123241827, "dur": 129, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123241959, "dur": 294, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123242255, "dur": 19, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123242417, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123242419, "dur": 173, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123242595, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123242598, "dur": 52, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123242652, "dur": 242, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123242896, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123243108, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123243166, "dur": 284, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123243452, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123243454, "dur": 181, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123243808, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123243811, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123243841, "dur": 69, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123243911, "dur": 232, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123244147, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123244343, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123244345, "dur": 187, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123244536, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123244715, "dur": 186, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123244903, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123244905, "dur": 21, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123245122, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123245124, "dur": 205, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123245333, "dur": 181, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123245516, "dur": 165, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123245800, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123245802, "dur": 26, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123245998, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123246000, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123246023, "dur": 3, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123246177, "dur": 31, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123246373, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123246396, "dur": 207, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123246769, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123246772, "dur": 225, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123246998, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123247131, "dur": 198, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123247331, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123247333, "dur": 21, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123247358, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123247464, "dur": 163, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123247628, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123247629, "dur": 17, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123247777, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123247934, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123248104, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123248130, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123248170, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123248172, "dur": 158, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123248334, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123248519, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123248521, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123248715, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123248718, "dur": 180, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123248901, "dur": 136, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123249039, "dur": 259, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123249300, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123249423, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123249601, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123249605, "dur": 57078, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123306693, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123306696, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123306756, "dur": 20, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123306777, "dur": 10771, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123317558, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123317561, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123317589, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123317628, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123317630, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123317649, "dur": 1100, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123318755, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123318793, "dur": 176, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123318974, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123319006, "dur": 622, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123319633, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123319649, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123319705, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123319738, "dur": 469, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123320212, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123320242, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123320244, "dur": 322, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123320572, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123320602, "dur": 279, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123320884, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123320904, "dur": 436, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123321346, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123321376, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123321378, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123321589, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123321612, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123321671, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123321699, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123321701, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123321728, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123321860, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123321890, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123321918, "dur": 740, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123322665, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123322701, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123322814, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123322837, "dur": 174, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123323015, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123323038, "dur": 141, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123323182, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123323212, "dur": 151, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123323366, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123323386, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123323477, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123323497, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123323537, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123323556, "dur": 799, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123324362, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123324383, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123324428, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123324450, "dur": 329, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123324782, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123324802, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123324869, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123324891, "dur": 173, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123325067, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123325094, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123325166, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123325187, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123325208, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123325226, "dur": 1284, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123326514, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123326545, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123326546, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123326584, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123326603, "dur": 604, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123327211, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123327237, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123327323, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123327351, "dur": 484, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123327839, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123327864, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123327937, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123327966, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123328048, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123328065, "dur": 579, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123328647, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123328667, "dur": 181, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123328852, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123328872, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123328892, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123328913, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123328915, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123328945, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123328974, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123328976, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329003, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329026, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329051, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329069, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329134, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329152, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329172, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329191, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329222, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329251, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329279, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329301, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329321, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329340, "dur": 24, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329369, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329401, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329426, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329458, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329480, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329511, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329538, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329557, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329584, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329609, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329635, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329670, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329696, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329716, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329718, "dur": 17, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329737, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329738, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329773, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329795, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329821, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329824, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329850, "dur": 18, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329870, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329888, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123329911, "dur": 140, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123330053, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123330079, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123330112, "dur": 134, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123330251, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123330274, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123330342, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123330363, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123330389, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123330421, "dur": 183, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123330607, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123330635, "dur": 475, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331115, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331116, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331148, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331172, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331213, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331246, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331270, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331291, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331319, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331321, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331345, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331400, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331425, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331448, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331490, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331517, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331545, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331571, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331659, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331661, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331685, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123331712, "dur": 234151, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123565875, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123565880, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123565919, "dur": 30, "ph": "X", "name": "ProcessMessages 20257", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123565950, "dur": 7317, "ph": "X", "name": "ReadAsync 20257", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123573273, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123573276, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 68719476736, "ts": 1756458123573316, "dur": 8536, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 23380, "tid": 124, "ts": 1756458123582356, "dur": 778, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 23380, "tid": 64424509440, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 23380, "tid": 64424509440, "ts": 1756458123186928, "dur": 107895, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 23380, "tid": 64424509440, "ts": 1756458123294825, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 23380, "tid": 64424509440, "ts": 1756458123294828, "dur": 70, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 23380, "tid": 124, "ts": 1756458123583136, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 23380, "tid": 60129542144, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 23380, "tid": 60129542144, "ts": 1756458123183952, "dur": 397948, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 23380, "tid": 60129542144, "ts": 1756458123184080, "dur": 2578, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 23380, "tid": 60129542144, "ts": 1756458123581905, "dur": 37, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 23380, "tid": 60129542144, "ts": 1756458123581915, "dur": 14, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 23380, "tid": 124, "ts": 1756458123583141, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1756458123204538, "dur": 128, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458123204698, "dur": 2614, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458123207323, "dur": 806, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458123208263, "dur": 94, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1756458123208357, "dur": 634, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458123209223, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_3B9BDFCC211E9D6D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756458123209809, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_0A215BF4FB7B7213.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756458123210057, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_8B6A19FAD6AE8D95.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756458123217394, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756458123219138, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1756458123222527, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756458123225473, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1756458123227347, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756458123209018, "dur": 19402, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458123228433, "dur": 342271, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458123570705, "dur": 427, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458123573036, "dur": 104, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458123573180, "dur": 1813, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1756458123209667, "dur": 18775, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123228470, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123228533, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_24A430C27796CDE1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458123228872, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_F6CB7DECB5054A4C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458123228977, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_F6CB7DECB5054A4C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458123229162, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_225D87A8B9941FD2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458123229532, "dur": 619, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756458123230166, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1756458123230282, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756458123230496, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9275553971400419791.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756458123230590, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756458123230743, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756458123230811, "dur": 3400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123234212, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123234417, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123234621, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123234823, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123235014, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123235266, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123236042, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123236335, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123236668, "dur": 549, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Flow\\Framework\\Control\\SwitchOnIntegerDescriptor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756458123236607, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123237377, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123237599, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123237862, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123238128, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123238636, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123238868, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123239110, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123239380, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123239642, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123239889, "dur": 61, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123239950, "dur": 1044, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123240994, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123241369, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458123241733, "dur": 569, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123242308, "dur": 2191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756458123244571, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458123244766, "dur": 835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756458123245696, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756458123246428, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458123246512, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756458123246844, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458123246935, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756458123247410, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458123247480, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756458123247790, "dur": 820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123248610, "dur": 66683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123315301, "dur": 2193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756458123317546, "dur": 5331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756458123322878, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123322940, "dur": 6641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756458123329637, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123329796, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123330170, "dur": 1050, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458123331220, "dur": 239517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123209735, "dur": 18789, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123228531, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_067C0613C0E0DB9E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458123228795, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123228862, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_490EB11FD412818E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458123228944, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_34CF52469E715BFE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458123229119, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123229212, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_37F3F60026223561.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458123229417, "dur": 242, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1756458123229738, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756458123229817, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756458123230096, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1756458123230295, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756458123230380, "dur": 245, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756458123230626, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756458123230810, "dur": 3986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123234797, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123235216, "dur": 1266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123236642, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.State\\INesterStateTransition.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756458123236482, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123237253, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123237431, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123237808, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123238374, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123239285, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123239458, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123239646, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123240169, "dur": 836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123241005, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123241481, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458123242313, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756458123243069, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123243227, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458123243404, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756458123244097, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123244449, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123244578, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123245040, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123245695, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458123245880, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756458123246307, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123246858, "dur": 781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123247640, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458123247736, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756458123248021, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123248639, "dur": 66680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123315327, "dur": 5081, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756458123320409, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123320485, "dur": 4447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756458123324984, "dur": 2809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756458123327794, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458123327857, "dur": 3128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756458123331039, "dur": 239672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123209834, "dur": 18787, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123228628, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_ECC6BD7BBFF90F85.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458123229119, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_BCAB1FFD58D166C0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458123229269, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_29DAA4DCAC1D5694.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458123229410, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1756458123229706, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756458123229965, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1756458123230113, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1756458123230287, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756458123230402, "dur": 299, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756458123230801, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123231254, "dur": 3915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123235170, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123235392, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123236077, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123236327, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123236639, "dur": 852, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Flow\\FlowCanvas.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756458123236639, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123237993, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123238397, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123238734, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123238944, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123239766, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123240560, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123241007, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123241402, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458123241793, "dur": 1091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756458123242884, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123243062, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123243228, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458123243385, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756458123244109, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123244450, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123244590, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123245082, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123245715, "dur": 1125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123246841, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458123246961, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756458123247407, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458123247509, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756458123247862, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458123247963, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756458123248263, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123248631, "dur": 66643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123315281, "dur": 3969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756458123319251, "dur": 861, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123320123, "dur": 3576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756458123323699, "dur": 554, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123324261, "dur": 4763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756458123329090, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123329540, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123329661, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123329815, "dur": 712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123330531, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458123331075, "dur": 239751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123209724, "dur": 18790, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123228521, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_7A4BC2D416416A7B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458123228882, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_7A4BC2D416416A7B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458123229011, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_C9598539BB3211AB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458123229096, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_644596ED4FF596EA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458123229460, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1756458123229724, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1756458123230115, "dur": 326, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1756458123230466, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1756458123230610, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1756458123230724, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15855758940564181747.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1756458123230823, "dur": 3354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123234178, "dur": 131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123234309, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123234456, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123234652, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123234827, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123235030, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123235310, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123236031, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123236368, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123236573, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123237643, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123238112, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123238607, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123238993, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123239345, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123239547, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123239929, "dur": 1058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123240988, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123241383, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458123241667, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756458123242235, "dur": 662, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123242934, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458123243249, "dur": 693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756458123243982, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123244262, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458123244361, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756458123244702, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123244894, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123245051, "dur": 657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123245708, "dur": 1217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123246925, "dur": 1659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123248584, "dur": 49240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123297825, "dur": 17420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123315254, "dur": 2173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1756458123317427, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123317482, "dur": 3865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1756458123321348, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123321616, "dur": 5481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1756458123327127, "dur": 3126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1756458123330313, "dur": 1140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458123331482, "dur": 239265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458123209822, "dur": 18784, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458123228618, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_A4DE870A30755667.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458123228862, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_AFF857DF058FD9A1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458123228943, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_5A592AA39DE2E737.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458123229003, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_5A592AA39DE2E737.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458123229117, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_F74E89BA682F97FF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458123229417, "dur": 481, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_7BAC05683089B125.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458123229909, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458123230001, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458123230201, "dur": 8312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756458123238606, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458123239072, "dur": 1832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756458123241024, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458123241092, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756458123241370, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458123241466, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458123241912, "dur": 1234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756458123243214, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458123243604, "dur": 762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756458123244450, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458123244588, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756458123245039, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1756458123245479, "dur": 80, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458123245913, "dur": 60672, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1756458123315229, "dur": 2210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1756458123317470, "dur": 2035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1756458123319551, "dur": 1978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1756458123321530, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458123321587, "dur": 1981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1756458123323569, "dur": 768, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458123324345, "dur": 2046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1756458123326435, "dur": 1962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1756458123328397, "dur": 427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458123328861, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458123328975, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458123329351, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458123329495, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458123329561, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458123329815, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458123330308, "dur": 1108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458123331442, "dur": 239394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123209668, "dur": 18799, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123228479, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123228552, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_C393B5AAA8F80126.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458123228911, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_8B6A19FAD6AE8D95.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458123228976, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_8B6A19FAD6AE8D95.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458123229151, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_EF2C1B44647AC877.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458123229382, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123229518, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756458123229746, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756458123229829, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1756458123230118, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1756458123230454, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756458123230577, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756458123230677, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16659839770564295142.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756458123230798, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123231221, "dur": 3630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123234851, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123235183, "dur": 745, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Util\\TextUtil.cs"}}, {"pid": 12345, "tid": 6, "ts": 1756458123235096, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123236092, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123236668, "dur": 869, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.State\\StateGraph.cs"}}, {"pid": 12345, "tid": 6, "ts": 1756458123236470, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123237556, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123237904, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123238208, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123238460, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123238691, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123238925, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123239267, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123239556, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123240066, "dur": 935, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123241001, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123241483, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458123241926, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123242291, "dur": 1585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756458123243949, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123244437, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123244565, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458123244842, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123244940, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756458123245716, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458123245866, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756458123246407, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458123246498, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756458123246937, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458123247045, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756458123247356, "dur": 1223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123248581, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458123248723, "dur": 66585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123315316, "dur": 3484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1756458123318801, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123318887, "dur": 4175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1756458123323128, "dur": 3176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mobile.AndroidLogcat.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1756458123326305, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458123326505, "dur": 4682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1756458123331227, "dur": 239516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123210018, "dur": 18637, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123228661, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_FE5DA9A8F69DF848.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458123228733, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123228836, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_2403B35E96799645.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458123229048, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_7440D828B24F1399.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458123229115, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_9D2B67F1A653618B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458123229473, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458123229536, "dur": 389, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458123229938, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1756458123230078, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1756458123230350, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458123230464, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458123230571, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458123230700, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458123230787, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458123230865, "dur": 3918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123234783, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123235086, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123235399, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Graphs\\ShaderInput.cs"}}, {"pid": 12345, "tid": 7, "ts": 1756458123235376, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123236113, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123236377, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123236808, "dur": 752, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@943f3af6fe14\\Runtime\\GPUDriven\\InstanceData\\InstanceTransformUpdateDefs.cs"}}, {"pid": 12345, "tid": 7, "ts": 1756458123236762, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123237874, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123238276, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123238650, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123238899, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123239224, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123239508, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123239568, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123239946, "dur": 1049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123240996, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123241386, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458123242126, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123242564, "dur": 1290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756458123243914, "dur": 526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123244440, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123244574, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123245040, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123245696, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458123245820, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756458123246212, "dur": 667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123246879, "dur": 1726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123248606, "dur": 68762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123317374, "dur": 5318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1756458123322732, "dur": 5588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1756458123328321, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458123328576, "dur": 2841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1756458123331457, "dur": 239289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123209702, "dur": 18795, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123228518, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_C79C35D09E315F10.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458123228953, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_252F14131FE896F3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458123229093, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_C06DD15739D13551.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458123229340, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_EAB88A61CDB6E545.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458123229516, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1756458123229754, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756458123229825, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756458123229943, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756458123230186, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756458123230415, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1756458123230480, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756458123230664, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756458123230818, "dur": 3750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123234568, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123234766, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123235014, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123235288, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123236303, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123236580, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123236853, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123237045, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123237332, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123237594, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123237834, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123238089, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123238644, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123238895, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123239284, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123239695, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123240550, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123241008, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123241570, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458123242240, "dur": 1659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756458123243899, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123244068, "dur": 364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123244433, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123244566, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458123244893, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756458123245357, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123245716, "dur": 1196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123246912, "dur": 1671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123248583, "dur": 46240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123296995, "dur": 151, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.15f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 8, "ts": 1756458123297147, "dur": 621, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.15f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 8, "ts": 1756458123294824, "dur": 2995, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123297820, "dur": 17444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123315272, "dur": 3105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1756458123318378, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123318655, "dur": 4993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1756458123323649, "dur": 1048, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123324707, "dur": 5227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1756458123329997, "dur": 1136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458123331168, "dur": 239540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123209818, "dur": 18757, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123228584, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_76639DDD4EDD4D5F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458123228722, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_E30A78F0F4CABA72.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458123228777, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123228847, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_E30A78F0F4CABA72.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458123228928, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_413D72FD9D818F92.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458123228979, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123229094, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_697D3802CF3E27D6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458123229515, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1756458123229716, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756458123229840, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756458123230182, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756458123230416, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1756458123230689, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756458123230886, "dur": 3268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123234154, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123234313, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123234462, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123234862, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123235094, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123235347, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123236541, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123236791, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123236988, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123237183, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123237421, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123237790, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123238046, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123238409, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123238651, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123238878, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123239788, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123240377, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123241002, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123241406, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458123241931, "dur": 1712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756458123243644, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123243751, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458123244024, "dur": 820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756458123244882, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123245060, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123245694, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458123245875, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756458123246304, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123246865, "dur": 1729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123248594, "dur": 68806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123317400, "dur": 5955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.iOS.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1756458123323357, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123323474, "dur": 5975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1756458123329554, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123329654, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123329742, "dur": 511, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123330264, "dur": 1064, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458123331342, "dur": 239360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123209759, "dur": 18786, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123228562, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_D26119ABA3CBFB9B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458123228875, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_37A60DF2B0300951.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458123229127, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123229444, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_6FBDBF7066DCE3B7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458123229773, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756458123230123, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1756458123230237, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756458123230320, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756458123230702, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13408987163503134604.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756458123230794, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123231333, "dur": 4034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123235393, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Interfaces\\Graph\\INode.cs"}}, {"pid": 12345, "tid": 10, "ts": 1756458123235367, "dur": 1243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123236610, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123236846, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123237061, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123237349, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123237530, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123237810, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123238086, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123238350, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123238686, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123238947, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123239425, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123239936, "dur": 1053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123240989, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123241398, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458123242214, "dur": 1090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1756458123243305, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123243395, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123243774, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123244434, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123244566, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458123244840, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1756458123245247, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123245718, "dur": 1154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123246872, "dur": 1717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123248589, "dur": 66643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123315234, "dur": 6451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1756458123321686, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123321813, "dur": 5838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1756458123327651, "dur": 1112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458123328775, "dur": 2785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1756458123331590, "dur": 239225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123209791, "dur": 18773, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123228572, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_E57B6CA5B0D6757A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458123228876, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_E0F443DE57A60521.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458123228947, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_5860F673E9B806F3.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458123229101, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_3D3DFD266B8F6400.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458123229313, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A0B1B49A6ED86309.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458123229459, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1756458123229692, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756458123229828, "dur": 353, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756458123230380, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1756458123230780, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756458123230892, "dur": 2728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123233621, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123234460, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123234671, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123234888, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123235092, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123235397, "dur": 513, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Nodes\\Channel\\FlipNode.cs"}}, {"pid": 12345, "tid": 11, "ts": 1756458123235302, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123235992, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123236262, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123236564, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123236893, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123237092, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123237309, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123237494, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123237893, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123238256, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123238670, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123238865, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123239114, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123239932, "dur": 1053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123240986, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123241372, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458123241788, "dur": 1483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756458123243271, "dur": 946, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123244236, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123244456, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123244568, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123245039, "dur": 603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123245643, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458123245755, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123245873, "dur": 964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756458123246894, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458123247018, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756458123247637, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458123247759, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756458123248100, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458123248169, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756458123248377, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123248599, "dur": 68780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123317383, "dur": 4341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1756458123321725, "dur": 830, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123322571, "dur": 6898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1756458123329470, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123329777, "dur": 226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1756458123330004, "dur": 1185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458123331237, "dur": 239505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123209667, "dur": 18810, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123228485, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_3C8F4B722C6531CA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458123228720, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_76E612DE556D963F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458123228899, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_81EA7A5AC3CF9257.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458123229012, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_BEB649454B032951.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458123229124, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123229513, "dur": 249, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1756458123229764, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756458123229832, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756458123229939, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1756458123230077, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1756458123230185, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756458123230345, "dur": 229, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756458123230575, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2897256077774953845.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756458123230669, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756458123230807, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123231340, "dur": 3975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123235394, "dur": 534, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Legacy\\GraphData0.cs"}}, {"pid": 12345, "tid": 12, "ts": 1756458123235315, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123236102, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123236415, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123236981, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123237200, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123237433, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123237694, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123238104, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123238440, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123238653, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123238920, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123239312, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123239535, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123239784, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123240526, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123241008, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123241406, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458123242024, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123242136, "dur": 1551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756458123243780, "dur": 650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123244431, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458123244573, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756458123245182, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123245302, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123245716, "dur": 1182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123246898, "dur": 1683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123248582, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458123248731, "dur": 66606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123315346, "dur": 5487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1756458123320834, "dur": 666, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123321511, "dur": 5692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1756458123327204, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458123327266, "dur": 4045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1756458123331355, "dur": 239407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123209888, "dur": 18743, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123228641, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_113F580BF08E1069.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458123228790, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123228845, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_E9AE314D2820AA3D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458123228916, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_E5AF82105D3AFD80.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458123229118, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123229337, "dur": 250, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_AE4FFD05F932783C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458123229687, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756458123229826, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1756458123229898, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756458123229997, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756458123230271, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756458123230452, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1756458123230660, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13938709970307456411.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756458123230826, "dur": 2991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123233818, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123233996, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123234159, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123234359, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123234554, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123234763, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123235373, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123236111, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123236366, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123236607, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123236880, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123237149, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123237460, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123237717, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123237953, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123238197, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123238429, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123238806, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123239025, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123239640, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123239870, "dur": 71, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123239941, "dur": 1043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123241012, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123241380, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458123241717, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123241911, "dur": 1009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756458123242991, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458123243426, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123243482, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756458123244214, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123244454, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123244577, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123245068, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123245705, "dur": 1234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458123246940, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458123247089, "dur": 904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756458123248077, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458123248148, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756458123248582, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458123248715, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756458123249006, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756458123249300, "dur": 316623, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756458123209964, "dur": 18681, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123228651, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1560055E669A2E68.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458123228730, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_2C44277543DF4EF4.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458123228840, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_2C44277543DF4EF4.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458123229117, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123229228, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_6AB2B904AF5C82BF.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458123229416, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756458123229519, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1756458123229693, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756458123229814, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1756458123229906, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756458123230109, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756458123230173, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1756458123230279, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756458123230380, "dur": 291, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1756458123230672, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756458123230783, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756458123230854, "dur": 3298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123234153, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123234322, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123234500, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123234688, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123234918, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123235152, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123235395, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123236128, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123236728, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123236910, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123237106, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123237311, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123237508, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123238126, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123238647, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123238838, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123239585, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123240145, "dur": 870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123241015, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123241375, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458123241858, "dur": 1075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756458123242934, "dur": 533, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123243503, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123243773, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123244100, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123244451, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123244583, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123245067, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123245711, "dur": 1130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123246842, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458123246933, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756458123247202, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123247374, "dur": 1278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123248652, "dur": 66631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123315290, "dur": 4699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1756458123319990, "dur": 806, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123320804, "dur": 3952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1756458123324793, "dur": 3032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1756458123327826, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458123327971, "dur": 3190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1756458123331195, "dur": 239515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123210048, "dur": 18616, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123228671, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_2C4CF458CCFD79E5.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458123228777, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_0A215BF4FB7B7213.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458123229054, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D30F86F3A61014E6.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458123229105, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_0CB44D3D3D7D3B27.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458123229409, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_9669307A6C8A8027.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458123229507, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1756458123229783, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756458123229910, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756458123230083, "dur": 427, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1756458123230511, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756458123230654, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123230742, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756458123230822, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123231041, "dur": 3347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123234389, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123234562, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123235213, "dur": 690, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Drawing\\Inspector\\PropertyDrawers\\TransformNodePropertyDrawer.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756458123235061, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123236037, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123236449, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123236766, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123236964, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123237164, "dur": 509, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Inspection\\Reflection\\NamespaceInspector.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756458123237131, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123237825, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123238187, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123238373, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123238673, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123239562, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123240061, "dur": 930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123240992, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123241483, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458123241669, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123241968, "dur": 798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756458123242767, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123242951, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123243052, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123243228, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123243776, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123244432, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458123244639, "dur": 643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756458123245344, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123245714, "dur": 1205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123246920, "dur": 1686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123248606, "dur": 66823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123315435, "dur": 7090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1756458123322526, "dur": 744, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123323280, "dur": 5511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1756458123328792, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123328932, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123329352, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123329596, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1756458123329806, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123329980, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123330266, "dur": 1076, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458123331342, "dur": 239398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123210075, "dur": 18602, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123228683, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_90248CE893D716A1.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458123228770, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_BF95CA48BE5F8E7B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458123229029, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_4A58700C3C59D067.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458123229103, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_C70E350A8365786E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458123229498, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1756458123229691, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1756458123230096, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1756458123230348, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1756458123230461, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756458123230687, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756458123230875, "dur": 3525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123234400, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123234682, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123234894, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123235308, "dur": 596, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Nodes\\Procedural\\CheckerboardNode.cs"}}, {"pid": 12345, "tid": 16, "ts": 1756458123235167, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123236039, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123236396, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123237175, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123237352, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123237853, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123238211, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123238464, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123239073, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123239510, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123239720, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123240639, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123240990, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123241368, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458123241523, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123241579, "dur": 955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756458123242603, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458123242805, "dur": 1309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756458123244114, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123244305, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123244433, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123244567, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458123244936, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756458123245387, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123245721, "dur": 1164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123246885, "dur": 1728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123248613, "dur": 66734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123315356, "dur": 4230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1756458123319626, "dur": 5137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1756458123324764, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458123325086, "dur": 2638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1756458123327757, "dur": 3538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1756458123331352, "dur": 239362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123210114, "dur": 18574, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123228905, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C3D287ED16DDCE34.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756458123229055, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_9B615D8E9656275F.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756458123229139, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_35E87E2AC2B68ABC.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756458123229267, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123229319, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_35E87E2AC2B68ABC.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756458123229453, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9BD5BA1E797F9120.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756458123229908, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756458123230010, "dur": 6968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1756458123237081, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123237981, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123238371, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123238843, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123239044, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123239318, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123239592, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123240090, "dur": 908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123240998, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123241408, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756458123241886, "dur": 1084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1756458123243009, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756458123243188, "dur": 854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1756458123244108, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123244455, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123244585, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123245046, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123245693, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756458123245808, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1756458123246142, "dur": 750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123246892, "dur": 1717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123248609, "dur": 66719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123315335, "dur": 7898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1756458123323234, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458123323401, "dur": 5144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1756458123328570, "dur": 2818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1756458123331418, "dur": 239333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123210149, "dur": 18548, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123228705, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_0B4B7F18296A2318.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458123228795, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_0B4B7F18296A2318.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458123228977, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_6E00AF4025AF0388.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458123229103, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123229281, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_3B9BDFCC211E9D6D.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458123229415, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1756458123229542, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756458123229740, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756458123230173, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1756458123230413, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756458123230598, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7043721260763788226.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756458123230729, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756458123230818, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123231019, "dur": 2656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123233675, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123233822, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123233992, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123234145, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123234325, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123234514, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123234709, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123234895, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123235389, "dur": 520, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Nodes\\UV\\ParallaxOcclusionMappingNode.cs"}}, {"pid": 12345, "tid": 18, "ts": 1756458123235099, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123235988, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123236291, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123236878, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123237093, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123237308, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123237493, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123237732, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123238422, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123238696, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123239005, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123239253, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123239511, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123239938, "dur": 1044, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123241007, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123241402, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458123241824, "dur": 2396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1756458123244266, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458123244363, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1756458123244729, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123245076, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123245711, "dur": 1194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123246905, "dur": 1696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123248601, "dur": 66755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123315362, "dur": 6375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1756458123321785, "dur": 6938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1756458123328724, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123328843, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123328932, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123329391, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123329550, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Editor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1756458123329721, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123329819, "dur": 1210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123331032, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458123331127, "dur": 239685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123210201, "dur": 18508, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123228714, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_D8ABA312F2231113.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458123228775, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123228855, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_D8ABA312F2231113.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458123229052, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_171E53A1F78BE3A8.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458123229156, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_490D37C9590BB1EA.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458123229408, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458123229778, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756458123229878, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1756458123230013, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756458123230174, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1756458123230420, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756458123230634, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756458123230776, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123231244, "dur": 3521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123234765, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123235209, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123235400, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Attributes\\SubTargetFilterAttribute.cs"}}, {"pid": 12345, "tid": 19, "ts": 1756458123235399, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123236086, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123236400, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123236696, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123236902, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123237090, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123237304, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123237502, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123237778, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123237975, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123238289, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123238830, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123239159, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123239388, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123239696, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123240545, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123241011, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123241535, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458123242183, "dur": 1497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1756458123243681, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123243775, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123244435, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123244572, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123245050, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123245703, "dur": 1147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123246850, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123246942, "dur": 1648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123248591, "dur": 66637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123315231, "dur": 5970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Unified.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1756458123321250, "dur": 5904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1756458123327163, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123327236, "dur": 3853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1756458123331132, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458123331322, "dur": 239384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123210227, "dur": 18498, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123228726, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_82E07B65278E8145.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458123228873, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_82E07B65278E8145.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458123228925, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_EC956E7CE4C11C28.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458123229059, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_7D653DAE049DA23B.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458123229146, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_AF6907722A59D8EC.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458123229311, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_7410C1EE6B5B3808.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458123229451, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_7410C1EE6B5B3808.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458123229660, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756458123229826, "dur": 270, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1756458123230121, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1756458123230258, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756458123230673, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756458123230798, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123231282, "dur": 3262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123234545, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123234742, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123235217, "dur": 689, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Nodes\\Math\\Round\\TruncateNode.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756458123235191, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123236139, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123236404, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123236749, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123236967, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123237177, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123237373, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123237575, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123238128, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123238449, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123238670, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123238994, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123239260, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123239517, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123239874, "dur": 77, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123239951, "dur": 1045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123240997, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123241391, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458123241959, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123242235, "dur": 1450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1756458123243686, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123243759, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123244137, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458123244288, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1756458123244966, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123245399, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123245700, "dur": 732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123246433, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458123246523, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1756458123246810, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123246933, "dur": 1663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123248596, "dur": 66639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123315236, "dur": 2096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1756458123317333, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123317426, "dur": 6857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1756458123324284, "dur": 840, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123325130, "dur": 4912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1756458123330042, "dur": 474, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123330537, "dur": 1049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458123331609, "dur": 239147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458123580365, "dur": 1048, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 23380, "tid": 124, "ts": 1756458123583168, "dur": 1720, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 23380, "tid": 124, "ts": 1756458123584913, "dur": 6323, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 23380, "tid": 124, "ts": 1756458123582319, "dur": 8956, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}