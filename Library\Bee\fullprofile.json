{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 23380, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 23380, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 23380, "tid": 135, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 23380, "tid": 135, "ts": 1756458659271780, "dur": 10, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 23380, "tid": 135, "ts": 1756458659271804, "dur": 4, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 23380, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 23380, "tid": 1, "ts": 1756458659067336, "dur": 1129, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 23380, "tid": 1, "ts": 1756458659068467, "dur": 23821, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 23380, "tid": 1, "ts": 1756458659092290, "dur": 30225, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 23380, "tid": 135, "ts": 1756458659271809, "dur": 6, "ph": "X", "name": "", "args": {}}, {"pid": 23380, "tid": 81604378624, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659067301, "dur": 15864, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659083167, "dur": 188159, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659083179, "dur": 74, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659083258, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659083261, "dur": 339, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659083603, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659083605, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659083632, "dur": 9, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659083642, "dur": 3088, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659086739, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659086743, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659086776, "dur": 1, "ph": "X", "name": "ProcessMessages 1579", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659086778, "dur": 27, "ph": "X", "name": "ReadAsync 1579", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659086810, "dur": 3, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659086816, "dur": 45, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659086864, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659086866, "dur": 34, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659086904, "dur": 39, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659086946, "dur": 26, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659086975, "dur": 28, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087007, "dur": 1, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087011, "dur": 24, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087038, "dur": 35, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087077, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087106, "dur": 18, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087126, "dur": 17, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087146, "dur": 24, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087172, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087175, "dur": 32, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087212, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087214, "dur": 71, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087286, "dur": 1, "ph": "X", "name": "ProcessMessages 895", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087288, "dur": 27, "ph": "X", "name": "ReadAsync 895", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087318, "dur": 19, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087340, "dur": 31, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087373, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087375, "dur": 29, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087407, "dur": 34, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087443, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087446, "dur": 23, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087472, "dur": 23, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087497, "dur": 23, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087524, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087527, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087553, "dur": 24, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087579, "dur": 46, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087628, "dur": 35, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087665, "dur": 2, "ph": "X", "name": "ProcessMessages 915", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087667, "dur": 25, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087696, "dur": 22, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087722, "dur": 38, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087763, "dur": 34, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087800, "dur": 26, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087828, "dur": 46, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087876, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087878, "dur": 29, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087909, "dur": 1, "ph": "X", "name": "ProcessMessages 903", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087911, "dur": 40, "ph": "X", "name": "ReadAsync 903", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087954, "dur": 29, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087985, "dur": 1, "ph": "X", "name": "ProcessMessages 1021", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659087988, "dur": 41, "ph": "X", "name": "ReadAsync 1021", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088030, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088032, "dur": 37, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088072, "dur": 23, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088098, "dur": 34, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088134, "dur": 49, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088185, "dur": 2, "ph": "X", "name": "ProcessMessages 917", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088188, "dur": 29, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088219, "dur": 3, "ph": "X", "name": "ProcessMessages 1121", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088222, "dur": 28, "ph": "X", "name": "ReadAsync 1121", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088251, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088253, "dur": 32, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088287, "dur": 82, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088372, "dur": 28, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088404, "dur": 21, "ph": "X", "name": "ReadAsync 1221", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088427, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088429, "dur": 21, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088452, "dur": 26, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088480, "dur": 25, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088507, "dur": 28, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088538, "dur": 4, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088543, "dur": 24, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088571, "dur": 40, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088613, "dur": 1, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088615, "dur": 36, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088655, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088657, "dur": 41, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088698, "dur": 1, "ph": "X", "name": "ProcessMessages 947", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088700, "dur": 30, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088734, "dur": 1, "ph": "X", "name": "ProcessMessages 1035", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088736, "dur": 52, "ph": "X", "name": "ReadAsync 1035", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088789, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088792, "dur": 26, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088819, "dur": 1, "ph": "X", "name": "ProcessMessages 884", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088821, "dur": 41, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088865, "dur": 54, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088922, "dur": 39, "ph": "X", "name": "ReadAsync 1194", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088963, "dur": 25, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088989, "dur": 2, "ph": "X", "name": "ProcessMessages 1049", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659088992, "dur": 43, "ph": "X", "name": "ReadAsync 1049", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089037, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089038, "dur": 25, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089066, "dur": 2, "ph": "X", "name": "ProcessMessages 1105", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089069, "dur": 33, "ph": "X", "name": "ReadAsync 1105", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089105, "dur": 17, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089124, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089125, "dur": 32, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089161, "dur": 47, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089210, "dur": 44, "ph": "X", "name": "ReadAsync 1145", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089257, "dur": 23, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089282, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089285, "dur": 32, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089320, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089373, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089414, "dur": 31, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089449, "dur": 44, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089496, "dur": 22, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089520, "dur": 42, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089565, "dur": 23, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089589, "dur": 1, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089592, "dur": 17, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089612, "dur": 17, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089634, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089662, "dur": 22, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089686, "dur": 36, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089725, "dur": 24, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089751, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089753, "dur": 18, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089772, "dur": 21, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089796, "dur": 41, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089838, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089840, "dur": 43, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089887, "dur": 38, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089928, "dur": 23, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089954, "dur": 38, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659089994, "dur": 23, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090019, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090023, "dur": 37, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090061, "dur": 24, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090086, "dur": 1, "ph": "X", "name": "ProcessMessages 950", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090090, "dur": 23, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090115, "dur": 19, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090136, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090138, "dur": 30, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090171, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090199, "dur": 21, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090222, "dur": 24, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090247, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090249, "dur": 37, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090289, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090311, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090312, "dur": 37, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090351, "dur": 45, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090399, "dur": 40, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090444, "dur": 41, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090488, "dur": 40, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090530, "dur": 37, "ph": "X", "name": "ReadAsync 850", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090570, "dur": 42, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090614, "dur": 1, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090616, "dur": 28, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090646, "dur": 47, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090695, "dur": 37, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090735, "dur": 42, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090779, "dur": 39, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090820, "dur": 2, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090823, "dur": 23, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090851, "dur": 3, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659090855, "dur": 267, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091126, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091128, "dur": 65, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091195, "dur": 3, "ph": "X", "name": "ProcessMessages 4158", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091201, "dur": 27, "ph": "X", "name": "ReadAsync 4158", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091233, "dur": 32, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091269, "dur": 2, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091272, "dur": 28, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091302, "dur": 95, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091399, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091427, "dur": 25, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091455, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091458, "dur": 27, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091487, "dur": 29, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091522, "dur": 26, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091551, "dur": 18, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091572, "dur": 22, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091599, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091628, "dur": 70, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091701, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091704, "dur": 42, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091748, "dur": 43, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091793, "dur": 26, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091822, "dur": 24, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091849, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659091851, "dur": 162, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092018, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092053, "dur": 1, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092055, "dur": 32, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092090, "dur": 24, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092117, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092119, "dur": 21, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092142, "dur": 46, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092192, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092220, "dur": 27, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092250, "dur": 25, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092276, "dur": 32, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092311, "dur": 27, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092341, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092343, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092368, "dur": 22, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092391, "dur": 10, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092404, "dur": 20, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092426, "dur": 18, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092446, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092470, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092472, "dur": 27, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092501, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092503, "dur": 22, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092527, "dur": 31, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092561, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092586, "dur": 19, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092606, "dur": 16, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092623, "dur": 21, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092646, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092670, "dur": 15, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092687, "dur": 23, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092714, "dur": 18, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092734, "dur": 37, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092772, "dur": 41, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092815, "dur": 20, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092838, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092840, "dur": 16, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092858, "dur": 14, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092873, "dur": 35, "ph": "X", "name": "ReadAsync 6", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092910, "dur": 24, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092936, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092938, "dur": 22, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092964, "dur": 28, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659092995, "dur": 25, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093023, "dur": 16, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093041, "dur": 19, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093062, "dur": 22, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093085, "dur": 18, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093105, "dur": 17, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093124, "dur": 40, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093166, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093196, "dur": 27, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093225, "dur": 24, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093251, "dur": 23, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093277, "dur": 29, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093309, "dur": 21, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093333, "dur": 22, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093357, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093383, "dur": 21, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093406, "dur": 25, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093434, "dur": 24, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093460, "dur": 13, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093475, "dur": 19, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093496, "dur": 18, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093517, "dur": 23, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093541, "dur": 22, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093566, "dur": 22, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093590, "dur": 20, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093613, "dur": 18, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093633, "dur": 19, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093654, "dur": 23, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093680, "dur": 18, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093701, "dur": 18, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093723, "dur": 46, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093781, "dur": 43, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093827, "dur": 23, "ph": "X", "name": "ReadAsync 1142", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093852, "dur": 36, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093891, "dur": 21, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093914, "dur": 2, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093917, "dur": 42, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659093962, "dur": 76, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094040, "dur": 1, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094042, "dur": 153, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094201, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094228, "dur": 22, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094252, "dur": 96, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094351, "dur": 40, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094395, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094397, "dur": 54, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094452, "dur": 1, "ph": "X", "name": "ProcessMessages 1506", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094454, "dur": 44, "ph": "X", "name": "ReadAsync 1506", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094501, "dur": 42, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094545, "dur": 1, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094547, "dur": 36, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094585, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094587, "dur": 33, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094622, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094624, "dur": 59, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094685, "dur": 49, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094736, "dur": 1, "ph": "X", "name": "ProcessMessages 1326", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094738, "dur": 30, "ph": "X", "name": "ReadAsync 1326", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094770, "dur": 20, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094792, "dur": 18, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094812, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094834, "dur": 22, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094858, "dur": 17, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094877, "dur": 26, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094906, "dur": 29, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094938, "dur": 21, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094961, "dur": 28, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659094992, "dur": 28, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095022, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095024, "dur": 28, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095054, "dur": 22, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095078, "dur": 18, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095097, "dur": 19, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095118, "dur": 49, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095168, "dur": 2, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095171, "dur": 23, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095197, "dur": 44, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095244, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095245, "dur": 27, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095274, "dur": 27, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095305, "dur": 31, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095338, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095340, "dur": 29, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095372, "dur": 19, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095392, "dur": 22, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095417, "dur": 20, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095441, "dur": 22, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095465, "dur": 38, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095505, "dur": 24, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095532, "dur": 24, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095560, "dur": 23, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095586, "dur": 37, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095625, "dur": 23, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095651, "dur": 18, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095670, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095671, "dur": 18, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095691, "dur": 26, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095719, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095742, "dur": 23, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095767, "dur": 19, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095788, "dur": 18, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095808, "dur": 17, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095827, "dur": 20, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095852, "dur": 17, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095871, "dur": 18, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095895, "dur": 18, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095915, "dur": 15, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095932, "dur": 9, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095942, "dur": 17, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095962, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659095989, "dur": 21, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096011, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096013, "dur": 19, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096034, "dur": 21, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096056, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096058, "dur": 18, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096078, "dur": 21, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096101, "dur": 19, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096121, "dur": 18, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096141, "dur": 18, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096161, "dur": 25, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096190, "dur": 31, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096224, "dur": 24, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096250, "dur": 16, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096267, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096286, "dur": 18, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096309, "dur": 23, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096334, "dur": 17, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096355, "dur": 21, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096378, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096380, "dur": 34, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096416, "dur": 22, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096440, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096442, "dur": 23, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096470, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096497, "dur": 28, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096529, "dur": 26, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096558, "dur": 62, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096623, "dur": 25, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096649, "dur": 39, "ph": "X", "name": "ReadAsync 42", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096691, "dur": 33, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096727, "dur": 24, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096753, "dur": 15, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096771, "dur": 21, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096795, "dur": 201, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659096998, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097018, "dur": 20, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097040, "dur": 19, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097061, "dur": 18, "ph": "X", "name": "ReadAsync 45", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097083, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097106, "dur": 22, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097130, "dur": 55, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097188, "dur": 26, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097216, "dur": 24, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097242, "dur": 26, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097271, "dur": 40, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097314, "dur": 24, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097340, "dur": 4, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097344, "dur": 28, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097375, "dur": 20, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097398, "dur": 26, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097441, "dur": 46, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097489, "dur": 20, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097511, "dur": 36, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097549, "dur": 42, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097593, "dur": 39, "ph": "X", "name": "ReadAsync 1119", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097636, "dur": 35, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097673, "dur": 57, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097734, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097768, "dur": 23, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097794, "dur": 35, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097831, "dur": 37, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097870, "dur": 30, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097904, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097906, "dur": 25, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097934, "dur": 27, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097962, "dur": 18, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659097982, "dur": 22, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098005, "dur": 19, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098026, "dur": 17, "ph": "X", "name": "ReadAsync 45", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098045, "dur": 10, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098056, "dur": 49, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098109, "dur": 46, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098158, "dur": 34, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098193, "dur": 34, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098229, "dur": 36, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098267, "dur": 41, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098310, "dur": 60, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098372, "dur": 37, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098410, "dur": 24, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098436, "dur": 5, "ph": "X", "name": "ProcessMessages 975", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098442, "dur": 26, "ph": "X", "name": "ReadAsync 975", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098470, "dur": 37, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098510, "dur": 32, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098545, "dur": 25, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098572, "dur": 28, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098602, "dur": 25, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098629, "dur": 19, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098650, "dur": 69, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098721, "dur": 22, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098745, "dur": 23, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098770, "dur": 26, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098797, "dur": 22, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098821, "dur": 44, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098867, "dur": 67, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098937, "dur": 1, "ph": "X", "name": "ProcessMessages 1883", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098939, "dur": 29, "ph": "X", "name": "ReadAsync 1883", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098970, "dur": 24, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659098996, "dur": 23, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099021, "dur": 36, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099058, "dur": 30, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099092, "dur": 33, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099127, "dur": 33, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099162, "dur": 44, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099208, "dur": 44, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099254, "dur": 1, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099256, "dur": 31, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099288, "dur": 2, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099292, "dur": 27, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099321, "dur": 20, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099344, "dur": 32, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099378, "dur": 18, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099398, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099399, "dur": 246, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099647, "dur": 239, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099888, "dur": 3, "ph": "X", "name": "ProcessMessages 5919", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099892, "dur": 69, "ph": "X", "name": "ReadAsync 5919", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099963, "dur": 3, "ph": "X", "name": "ProcessMessages 4189", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099967, "dur": 25, "ph": "X", "name": "ReadAsync 4189", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659099996, "dur": 38, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100036, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100038, "dur": 45, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100086, "dur": 1, "ph": "X", "name": "ProcessMessages 1100", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100088, "dur": 32, "ph": "X", "name": "ReadAsync 1100", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100122, "dur": 55, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100180, "dur": 33, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100217, "dur": 26, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100244, "dur": 1, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100246, "dur": 46, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100300, "dur": 16, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100318, "dur": 17, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100336, "dur": 11, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100350, "dur": 20, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100372, "dur": 28, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100403, "dur": 29, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100435, "dur": 21, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100458, "dur": 24, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100485, "dur": 21, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100507, "dur": 16, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100524, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100526, "dur": 47, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100581, "dur": 32, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100615, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100649, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100651, "dur": 44, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100697, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100699, "dur": 36, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100738, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100781, "dur": 20, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100802, "dur": 19, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100823, "dur": 14, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100839, "dur": 88, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100933, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100962, "dur": 22, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659100987, "dur": 20, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101010, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101068, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101096, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101097, "dur": 23, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101122, "dur": 87, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101212, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101239, "dur": 21, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101262, "dur": 18, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101282, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101333, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101357, "dur": 23, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101383, "dur": 28, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101412, "dur": 45, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101460, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101481, "dur": 22, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101505, "dur": 50, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101557, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101559, "dur": 23, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101584, "dur": 37, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101623, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101655, "dur": 31, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101689, "dur": 17, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101707, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101755, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101783, "dur": 21, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101806, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101807, "dur": 60, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101869, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101910, "dur": 23, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101936, "dur": 57, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659101995, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102021, "dur": 25, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102047, "dur": 15, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102064, "dur": 55, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102121, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102156, "dur": 20, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102177, "dur": 53, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102234, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102286, "dur": 20, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102310, "dur": 41, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102354, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102382, "dur": 20, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102405, "dur": 26, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102433, "dur": 50, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102485, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102518, "dur": 20, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102540, "dur": 58, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102601, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102636, "dur": 30, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102669, "dur": 44, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102715, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102748, "dur": 26, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102776, "dur": 47, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102825, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102849, "dur": 32, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102882, "dur": 24, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102909, "dur": 48, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102961, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102996, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659102998, "dur": 18, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103017, "dur": 58, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103077, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103079, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103127, "dur": 18, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103147, "dur": 48, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103197, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103225, "dur": 1, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103227, "dur": 25, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103257, "dur": 17, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103276, "dur": 39, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103317, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103335, "dur": 21, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103358, "dur": 17, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103378, "dur": 20, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103399, "dur": 37, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103438, "dur": 37, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103478, "dur": 32, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103513, "dur": 29, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103546, "dur": 24, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103571, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103597, "dur": 20, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103620, "dur": 18, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103641, "dur": 82, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103725, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103752, "dur": 19, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103773, "dur": 30, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103805, "dur": 26, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103833, "dur": 18, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103852, "dur": 13, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103868, "dur": 65, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103935, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103971, "dur": 1, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103973, "dur": 18, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659103993, "dur": 41, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104037, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104061, "dur": 20, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104083, "dur": 20, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104105, "dur": 40, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104148, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104175, "dur": 31, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104208, "dur": 31, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104241, "dur": 112, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104355, "dur": 35, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104392, "dur": 1, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104393, "dur": 30, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104425, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104450, "dur": 31, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104482, "dur": 52, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104536, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104563, "dur": 19, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104585, "dur": 58, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104645, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104696, "dur": 1, "ph": "X", "name": "ProcessMessages 1050", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104697, "dur": 51, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104750, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104779, "dur": 33, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104814, "dur": 32, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104850, "dur": 1, "ph": "X", "name": "ProcessMessages 892", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104852, "dur": 26, "ph": "X", "name": "ReadAsync 892", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104879, "dur": 17, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104898, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104916, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659104946, "dur": 58, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105006, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105042, "dur": 19, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105066, "dur": 111, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105180, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105208, "dur": 1, "ph": "X", "name": "ProcessMessages 1078", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105210, "dur": 17, "ph": "X", "name": "ReadAsync 1078", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105229, "dur": 16, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105247, "dur": 26, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105276, "dur": 35, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105313, "dur": 29, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105344, "dur": 19, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105365, "dur": 69, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105437, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105458, "dur": 22, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105482, "dur": 28, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105513, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105537, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105594, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105620, "dur": 23, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105645, "dur": 29, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105676, "dur": 63, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105741, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105792, "dur": 27, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105822, "dur": 27, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105851, "dur": 61, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105914, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105939, "dur": 22, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105964, "dur": 26, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659105992, "dur": 67, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106061, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106097, "dur": 24, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106123, "dur": 47, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106172, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106200, "dur": 20, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106223, "dur": 59, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106284, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106318, "dur": 18, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106338, "dur": 57, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106397, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106422, "dur": 39, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106462, "dur": 16, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106480, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106482, "dur": 81, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106565, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106593, "dur": 4, "ph": "X", "name": "ProcessMessages 1252", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106598, "dur": 47, "ph": "X", "name": "ReadAsync 1252", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106647, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106671, "dur": 34, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106708, "dur": 22, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106733, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106735, "dur": 76, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106813, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106860, "dur": 1, "ph": "X", "name": "ProcessMessages 1129", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106862, "dur": 44, "ph": "X", "name": "ReadAsync 1129", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106909, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106949, "dur": 33, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659106984, "dur": 15, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107002, "dur": 73, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107077, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107102, "dur": 19, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107123, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107148, "dur": 55, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107205, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107253, "dur": 27, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107282, "dur": 15, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107299, "dur": 72, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107373, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107403, "dur": 22, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107429, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107431, "dur": 52, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107485, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107506, "dur": 20, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107529, "dur": 21, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107552, "dur": 15, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107569, "dur": 67, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107639, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107725, "dur": 19, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107746, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107747, "dur": 21, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107775, "dur": 25, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107802, "dur": 76, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107885, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107914, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107916, "dur": 44, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107963, "dur": 18, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659107983, "dur": 65, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108051, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108053, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108101, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108103, "dur": 32, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108139, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108142, "dur": 41, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108185, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108212, "dur": 25, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108239, "dur": 25, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108267, "dur": 52, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108320, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108354, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108355, "dur": 29, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108386, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108388, "dur": 56, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108446, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108470, "dur": 23, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108496, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108522, "dur": 68, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108592, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108617, "dur": 24, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108643, "dur": 23, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108668, "dur": 27, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108697, "dur": 2, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108700, "dur": 43, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108746, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108773, "dur": 25, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108800, "dur": 2, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108802, "dur": 29, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108834, "dur": 57, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108893, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108919, "dur": 38, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108960, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108962, "dur": 20, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659108984, "dur": 30, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109017, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109040, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109041, "dur": 23, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109067, "dur": 18, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109087, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109110, "dur": 19, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109131, "dur": 18, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109151, "dur": 16, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109170, "dur": 16, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109188, "dur": 66, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109257, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109281, "dur": 20, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109305, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109328, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109330, "dur": 30, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109364, "dur": 21, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109392, "dur": 19, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109415, "dur": 16, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109433, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109482, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109506, "dur": 21, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109530, "dur": 19, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109552, "dur": 40, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109594, "dur": 19, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109615, "dur": 19, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109636, "dur": 16, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109654, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109710, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109731, "dur": 107, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659109840, "dur": 264, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659110120, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659110129, "dur": 116, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659110248, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659110250, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659110283, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659110310, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659110342, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659110366, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659110394, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659110424, "dur": 242, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659110668, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659110711, "dur": 2, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659110714, "dur": 21, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659110737, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659110771, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659110773, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659110825, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659110854, "dur": 273, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659111145, "dur": 6, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659111154, "dur": 169, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659111327, "dur": 21, "ph": "X", "name": "ProcessMessages 991", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659111401, "dur": 103, "ph": "X", "name": "ReadAsync 991", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659111509, "dur": 15, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659111526, "dur": 83, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659111613, "dur": 5, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659111621, "dur": 74, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659111699, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659111708, "dur": 79, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659111790, "dur": 7, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659111801, "dur": 119, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659111923, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659111926, "dur": 45, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659111974, "dur": 2, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659111977, "dur": 23, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112002, "dur": 3, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112005, "dur": 59, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112068, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112070, "dur": 25, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112098, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112099, "dur": 24, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112125, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112146, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112191, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112193, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112223, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112225, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112252, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112295, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112300, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112339, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112341, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112368, "dur": 17, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112387, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112416, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112450, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112472, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112496, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112520, "dur": 18, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112540, "dur": 22, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112564, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112590, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112619, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112621, "dur": 28, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112650, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112651, "dur": 19, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112672, "dur": 22, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112697, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112730, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112753, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112774, "dur": 19, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112795, "dur": 17, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112816, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112834, "dur": 14, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112850, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112920, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112951, "dur": 3, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659112955, "dur": 68, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113026, "dur": 78, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113105, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113107, "dur": 59, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113169, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113171, "dur": 27, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113200, "dur": 72, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113275, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113325, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113352, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113376, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113401, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113459, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113526, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113527, "dur": 49, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113579, "dur": 108, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113690, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113750, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113848, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659113880, "dur": 6053, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659119943, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659119947, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659119988, "dur": 2274, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659122266, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659122268, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659122304, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659122306, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659122344, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659122457, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659122478, "dur": 139, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659122621, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659122642, "dur": 1449, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659124095, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659124122, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659124124, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659124159, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659124187, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659124230, "dur": 374, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659124606, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659124632, "dur": 111, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659124746, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659124766, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659124802, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659124820, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659124881, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659124903, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659124937, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659124957, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659124988, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659125004, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659125143, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659125163, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659125198, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659125221, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659125245, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659125328, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659125355, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659125374, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659125416, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659125438, "dur": 494, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659125936, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659125965, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659125993, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126016, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126035, "dur": 19, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126056, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126076, "dur": 15, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126095, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126115, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126133, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126166, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126188, "dur": 15, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126205, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126334, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126351, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126376, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126402, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126404, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126458, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126479, "dur": 60, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126541, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126558, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126575, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126594, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126616, "dur": 18, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126636, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126661, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126681, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126698, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126721, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126784, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126806, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126823, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126846, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126932, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126934, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126960, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126978, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659126998, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127078, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127102, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127186, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127205, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127228, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127229, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127253, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127274, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127296, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127298, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127348, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127362, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127392, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127414, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127454, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127495, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127527, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127528, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127572, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127591, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127613, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127634, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127653, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127707, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127745, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127773, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127797, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127915, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127938, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659127990, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128010, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128012, "dur": 150, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128167, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128196, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128255, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128277, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128298, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128327, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128328, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128352, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128370, "dur": 143, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128517, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128552, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128554, "dur": 47, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128604, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128634, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128663, "dur": 269, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128935, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128937, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659128985, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129016, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129040, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129069, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129091, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129147, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129174, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129204, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129360, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129378, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129381, "dur": 95, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129478, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129519, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129521, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129569, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129593, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129623, "dur": 165, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129791, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129821, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129849, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129850, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129874, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129906, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129925, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659129981, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130005, "dur": 166, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130175, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130226, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130262, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130264, "dur": 143, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130411, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130490, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130519, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130582, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130605, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130607, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130710, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130733, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130801, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130840, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130871, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130897, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130971, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659130999, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131043, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131045, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131094, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131129, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131166, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131254, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131255, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131301, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131356, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131402, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131443, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131479, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131572, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131574, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131606, "dur": 199, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131807, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131810, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131878, "dur": 91, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659131972, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659132014, "dur": 112, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659132128, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659132173, "dur": 168, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659132345, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659132368, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659132370, "dur": 55654, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659188036, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659188039, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659188105, "dur": 24, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659188131, "dur": 8816, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659196958, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659196961, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659197002, "dur": 414, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659197421, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659197423, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659197468, "dur": 312, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659197784, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659197786, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659197818, "dur": 551, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659198374, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659198400, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659198401, "dur": 312, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659198715, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659198741, "dur": 445, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659199189, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659199191, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659199227, "dur": 770, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659200000, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659200042, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659200071, "dur": 232, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659200308, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659200337, "dur": 589, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659200929, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659200949, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659201096, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659201114, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659201230, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659201255, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659201315, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659201352, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659201353, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659201405, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659201424, "dur": 120, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659201546, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659201563, "dur": 236, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659201801, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659201828, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659201851, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659201873, "dur": 347, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659202222, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659202246, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659202348, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659202371, "dur": 761, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659203136, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659203165, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659203305, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659203329, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659203351, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659203375, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659203393, "dur": 880, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659204278, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659204306, "dur": 441, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659204751, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659204774, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659204889, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659204920, "dur": 589, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659205511, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659205532, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659205661, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659205678, "dur": 161, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659205841, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659205861, "dur": 383, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659206247, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659206268, "dur": 314, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659206585, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659206607, "dur": 226, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659206836, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659206857, "dur": 167, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207026, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207044, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207086, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207107, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207162, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207183, "dur": 143, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207328, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207353, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207380, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207381, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207405, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207422, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207439, "dur": 356, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207800, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207828, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207883, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207915, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207918, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207943, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659207984, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208009, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208043, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208060, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208077, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208098, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208120, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208140, "dur": 21, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208165, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208191, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208219, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208238, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208266, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208287, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208310, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208334, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208355, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208375, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208395, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208413, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208435, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208460, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208484, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208508, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208539, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208559, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208584, "dur": 18, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208604, "dur": 59, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208664, "dur": 122, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208788, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208789, "dur": 21, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208811, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208813, "dur": 24, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208839, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208841, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208866, "dur": 20, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208888, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208913, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208930, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208947, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659208963, "dur": 247, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659209212, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659209235, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659209237, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659209267, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659209270, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659209342, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659209370, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659209371, "dur": 89, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659209464, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659209484, "dur": 148, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659209635, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659209654, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659209658, "dur": 226, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659209886, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659209917, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659209919, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659209945, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659210015, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659210053, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659210074, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659210132, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659210161, "dur": 40, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659210205, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659210208, "dur": 34, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659210245, "dur": 32, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659210282, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659210306, "dur": 45914, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659256230, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659256234, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659256296, "dur": 23, "ph": "X", "name": "ProcessMessages 2948", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659256321, "dur": 6603, "ph": "X", "name": "ReadAsync 2948", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659262929, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659262932, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 81604378624, "ts": 1756458659262969, "dur": 8348, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 23380, "tid": 135, "ts": 1756458659271817, "dur": 1602, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 23380, "tid": 77309411328, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 23380, "tid": 77309411328, "ts": 1756458659067239, "dur": 55282, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 23380, "tid": 77309411328, "ts": 1756458659122523, "dur": 29, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 23380, "tid": 135, "ts": 1756458659273420, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 23380, "tid": 73014444032, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 23380, "tid": 73014444032, "ts": 1756458659063787, "dur": 207576, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 23380, "tid": 73014444032, "ts": 1756458659063939, "dur": 2717, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 23380, "tid": 73014444032, "ts": 1756458659271368, "dur": 42, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 23380, "tid": 73014444032, "ts": 1756458659271379, "dur": 16, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 23380, "tid": 135, "ts": 1756458659273429, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1756458659083344, "dur": 69, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458659083434, "dur": 1969, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458659085411, "dur": 796, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458659086338, "dur": 85, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1756458659086423, "dur": 584, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458659087201, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_A3FCC5129F44B4D8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756458659087590, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_7A4BC2D416416A7B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756458659087715, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_C393B5AAA8F80126.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756458659088660, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_69C0C0E5D468F8D0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756458659091503, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756458659093192, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1756458659093431, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756458659094357, "dur": 261, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756458659096924, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1756458659097023, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756458659097085, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756458659097339, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Editor.ref.dll_0FF7AC68384616B1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756458659097431, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1756458659097507, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1756458659099024, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_7F2C410A434F5518.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756458659100020, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756458659100280, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756458659106087, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1756458659107996, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1756458659108259, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1756458659087039, "dur": 23043, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458659110094, "dur": 151600, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458659261696, "dur": 167, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458659261864, "dur": 177, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458659263185, "dur": 61, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458659263272, "dur": 1662, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1756458659087182, "dur": 22924, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659110233, "dur": 951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_E57B6CA5B0D6757A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458659111331, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_3CF76F5C2290DA72.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458659111592, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756458659111790, "dur": 431, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756458659112312, "dur": 397, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756458659112818, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1756458659112932, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1756458659113044, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756458659113201, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1756458659113335, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9275553971400419791.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756458659113425, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17376126960855114080.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756458659113628, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659113828, "dur": 3473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659117302, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659117501, "dur": 123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659117624, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659117816, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659117967, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659118198, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659118396, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659118754, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659119469, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659120016, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659120376, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659120813, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659121321, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659121680, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659122004, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659122674, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659122989, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659123312, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659123634, "dur": 869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659124503, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659125000, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458659125249, "dur": 2256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756458659127551, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659127763, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458659127933, "dur": 1162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756458659129095, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458659129415, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458659129501, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756458659130182, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458659130268, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756458659130699, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458659130778, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756458659131374, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458659131451, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756458659131713, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458659131802, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756458659132242, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458659132314, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756458659132479, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756458659132743, "dur": 123831, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756458659087282, "dur": 22860, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659110152, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_C79C35D09E315F10.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458659110694, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_FA41A1679EA786AD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458659110783, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_F68287F406F56905.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458659110859, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_E5AF82105D3AFD80.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458659110935, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_252F14131FE896F3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458659111078, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_9ED85F7723A041B8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458659111250, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_EF2C1B44647AC877.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458659111428, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_EAA7351847C2744E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458659111552, "dur": 311, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1756458659111916, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_77BE30E9EA33DCEC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458659112366, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1756458659112592, "dur": 338, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1756458659112931, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756458659112999, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756458659113163, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756458659113443, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756458659113602, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659113793, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659114626, "dur": 549, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.15f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756458659114091, "dur": 3849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659117940, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659118201, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659118418, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659118694, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659119057, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659119879, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659120141, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659120737, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659121193, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659121520, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659121787, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659122187, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659122531, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659125495, "dur": 171, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.15f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 2, "ts": 1756458659125666, "dur": 657, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.15f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 2, "ts": 1756458659126323, "dur": 51, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.15f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 2, "ts": 1756458659122933, "dur": 3441, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659126375, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756458659126954, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659127195, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659127780, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659127892, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659128649, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659128754, "dur": 1445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659130199, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458659130269, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756458659130539, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659130701, "dur": 1533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659132234, "dur": 64816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659197051, "dur": 4206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.iOS.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756458659201285, "dur": 3258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756458659204544, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659204626, "dur": 475, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756458659205104, "dur": 4849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756458659209954, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659210242, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458659210409, "dur": 51284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659087202, "dur": 22919, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659110236, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_D26119ABA3CBFB9B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458659110891, "dur": 673, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_8B6A19FAD6AE8D95.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458659111581, "dur": 505, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1756458659112133, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756458659112343, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458659112421, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458659112563, "dur": 7629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756458659120279, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659120548, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659121133, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659121388, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659121623, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659121874, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659122159, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659122425, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659122805, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659123036, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659123342, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659123977, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659124509, "dur": 885, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659125395, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458659125721, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756458659126306, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659126449, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659126588, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659127046, "dur": 722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659127768, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659127895, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659128657, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659128762, "dur": 1914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659130677, "dur": 1541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659132218, "dur": 62686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659194905, "dur": 4427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756458659199380, "dur": 3280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756458659202706, "dur": 3466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756458659206205, "dur": 2953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756458659209194, "dur": 514, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659209714, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458659209990, "dur": 51731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659087271, "dur": 22860, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659110140, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_3C8F4B722C6531CA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458659110695, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_E9AE314D2820AA3D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458659110906, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_EC956E7CE4C11C28.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458659111080, "dur": 462, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_41FB4EBD778438F0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458659111584, "dur": 320, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1756458659111937, "dur": 374, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A5FBD79E52C0F38C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458659112367, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1756458659112514, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659112632, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659112729, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1756458659112989, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1756458659113188, "dur": 258, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1756458659113447, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1756458659113523, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1756458659113606, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1756458659113705, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659113890, "dur": 3405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659117296, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659117466, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659117604, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659117784, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659117966, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659118353, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659119004, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659119889, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659120174, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659120423, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659120700, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659121002, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659121310, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659121720, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659122018, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659122307, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659122956, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659123293, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659123571, "dur": 62, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659123633, "dur": 868, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659124502, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659124999, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458659125097, "dur": 1281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756458659126455, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458659126826, "dur": 826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756458659127718, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659127772, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659127894, "dur": 755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659128649, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659128751, "dur": 1103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659129855, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458659129964, "dur": 1089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756458659131054, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659131230, "dur": 1024, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659132255, "dur": 63137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659195394, "dur": 2607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1756458659198003, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659198131, "dur": 4326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1756458659202458, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458659202577, "dur": 4576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1756458659207189, "dur": 3272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1756458659210524, "dur": 51198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659087308, "dur": 22850, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659110171, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_7A4BC2D416416A7B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458659110776, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_8AEC105970967529.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458659110847, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_4936E8BF32CAB915.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458659110907, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659111026, "dur": 264, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_4936E8BF32CAB915.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458659111299, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_166FE659F547CA5D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458659111602, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1756458659111789, "dur": 281, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1756458659112104, "dur": 507, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1756458659112619, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1756458659112866, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659112953, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1756458659113436, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1756458659113516, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1756458659113624, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659113824, "dur": 521, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.15f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 5, "ts": 1756458659113823, "dur": 4309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659118132, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659118338, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659118699, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659118972, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659119785, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659120167, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659120518, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659120892, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659121325, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659121578, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659122043, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659122347, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659122840, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659123110, "dur": 106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659123216, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659123470, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659123987, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659124509, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659125020, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458659125557, "dur": 898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756458659126493, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659126550, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458659126697, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756458659127253, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659127340, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659127783, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659127890, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458659127995, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756458659128364, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659128655, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659128733, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458659128866, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756458659129297, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659129381, "dur": 1319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659130700, "dur": 1535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659132235, "dur": 63988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659196225, "dur": 2741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1756458659198966, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659199077, "dur": 3056, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1756458659202183, "dur": 4714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1756458659206941, "dur": 3200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1756458659210142, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458659210275, "dur": 51416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458659087337, "dur": 22835, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458659110183, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_067C0613C0E0DB9E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458659110787, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_79BD845FDB16458C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458659111063, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_3DBCED557457FCF9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458659111220, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_9D2B67F1A653618B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458659111312, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_F70218AE2376F572.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458659111380, "dur": 951, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_F70218AE2376F572.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458659112346, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458659112436, "dur": 10099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756458659122536, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458659122655, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458659122972, "dur": 1433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756458659124556, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756458659125036, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458659125310, "dur": 1156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756458659126466, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458659126547, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458659126833, "dur": 824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756458659127657, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458659127890, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458659128001, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756458659128733, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458659128882, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756458659129378, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458659129438, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756458659129850, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458659129922, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756458659130157, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458659130701, "dur": 1544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458659132246, "dur": 62664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458659194912, "dur": 7233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1756458659202146, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458659202225, "dur": 5251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mobile.AndroidLogcat.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1756458659207519, "dur": 3047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1756458659210636, "dur": 51072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659087393, "dur": 22790, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659110192, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_24A430C27796CDE1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458659110731, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_AFF857DF058FD9A1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458659111084, "dur": 316, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_233E025D43731532.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458659111402, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_7410C1EE6B5B3808.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458659111581, "dur": 669, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458659112358, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458659112455, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458659112588, "dur": 341, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458659112968, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659113021, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458659113176, "dur": 258, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458659113435, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3232911574759799904.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458659113504, "dur": 205, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3232911574759799904.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458659113731, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659114799, "dur": 542, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.15f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-rtlsupport-l1-1-0.dll"}}, {"pid": 12345, "tid": 7, "ts": 1756458659115342, "dur": 545, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.15f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-profile-l1-1-0.dll"}}, {"pid": 12345, "tid": 7, "ts": 1756458659116644, "dur": 510, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.15f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-namedpipe-l1-1-0.dll"}}, {"pid": 12345, "tid": 7, "ts": 1756458659117155, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.15f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-memory-l1-1-0.dll"}}, {"pid": 12345, "tid": 7, "ts": 1756458659114188, "dur": 5427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659119615, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659119874, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659120352, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659120708, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659121033, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659121699, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659122034, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659122371, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659122658, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1756458659122814, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659123032, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659123584, "dur": 54, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659123638, "dur": 870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659124508, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659125010, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458659125253, "dur": 1002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756458659126256, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659126383, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458659126741, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756458659127453, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458659127597, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756458659128125, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659128661, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659128797, "dur": 1891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659130688, "dur": 1536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659132224, "dur": 62706, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659194931, "dur": 5353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1756458659200285, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659200363, "dur": 6142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1756458659206506, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458659206604, "dur": 3848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1756458659210568, "dur": 51139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659087432, "dur": 22764, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659110197, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_C393B5AAA8F80126.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458659110700, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_BF95CA48BE5F8E7B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458659111084, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_6B2638CED2F6CE50.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458659111280, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_37F3F60026223561.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458659111451, "dur": 256, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_A3FCC5129F44B4D8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458659111807, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756458659111942, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756458659112224, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756458659112398, "dur": 276, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1756458659112703, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756458659112897, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1756458659113042, "dur": 371, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756458659113417, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756458659113596, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659113826, "dur": 4432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659118258, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659118810, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659119537, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659120017, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659120260, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659120625, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659120982, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659121271, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659121634, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659122152, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659122337, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659122748, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659123191, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659123553, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659123668, "dur": 838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659124506, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659125023, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458659125301, "dur": 1131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756458659126493, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659126908, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458659127160, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756458659127771, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659127859, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659127915, "dur": 766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659128681, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659128794, "dur": 1888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659130683, "dur": 1531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659132216, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458659132352, "dur": 64253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659196608, "dur": 4911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1756458659201520, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659201587, "dur": 6039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1756458659207628, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659207798, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659208284, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Notifications.Android.dll"}}, {"pid": 12345, "tid": 8, "ts": 1756458659208371, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659208486, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659208562, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659208787, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1756458659209040, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659209215, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1756458659209316, "dur": 952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458659210294, "dur": 51430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659087482, "dur": 22737, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659110222, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_A4DE870A30755667.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458659111088, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_9D804A336986938E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458659111266, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_490D37C9590BB1EA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458659111339, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_490D37C9590BB1EA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458659111498, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458659111601, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458659111787, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1756458659111941, "dur": 304, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756458659112362, "dur": 346, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756458659112944, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756458659113189, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1756458659113384, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2897256077774953845.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756458659113516, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10131411833777065430.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756458659113604, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659113798, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659113966, "dur": 2994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659116961, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659118093, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659118299, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659118660, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659118983, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659119675, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659120036, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659120365, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659120835, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659121161, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659121519, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659121878, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659122200, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659122423, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659122865, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659123342, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659123972, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659124517, "dur": 863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659125380, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458659125771, "dur": 1129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756458659127060, "dur": 705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659127766, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458659127983, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756458659128500, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659128659, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659128729, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458659128890, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756458659129293, "dur": 1401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659130694, "dur": 1534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659132229, "dur": 63211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659195442, "dur": 6288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1756458659201732, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659201898, "dur": 5468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1756458659207367, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458659207446, "dur": 2893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1756458659210407, "dur": 51283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659087515, "dur": 22792, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659110307, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_414CE17575401FA5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458659111273, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_225D87A8B9941FD2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458659111340, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_225D87A8B9941FD2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458659111513, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9BD5BA1E797F9120.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458659111571, "dur": 275, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9BD5BA1E797F9120.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458659111847, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756458659111910, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756458659111994, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756458659112117, "dur": 525, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756458659112708, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756458659112965, "dur": 506, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": ****************, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": ****************, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": ****************, "dur": 568, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.15f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Localization.Routing.dll"}}, {"pid": 12345, "tid": 10, "ts": ****************, "dur": 4050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": ****************, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": ****************, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": ****************, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": ****************, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": ****************, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": ****************, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659120444, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659120860, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659121169, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659121489, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659121805, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659122048, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659122353, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659122668, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659123042, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659123334, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659123879, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659124524, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659125063, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458659125758, "dur": 544, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659126308, "dur": 1407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1756458659127782, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458659128062, "dur": 862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1756458659129010, "dur": 1692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659130702, "dur": 1548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659132250, "dur": 62676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659194931, "dur": 5414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1756458659200395, "dur": 4822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1756458659205253, "dur": 2760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1756458659208014, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659208297, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659208439, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659208686, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1756458659208799, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659209266, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659209719, "dur": 828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458659210547, "dur": 51155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659087572, "dur": 22722, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659110295, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_90248CE893D716A1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458659111079, "dur": 320, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_4A58700C3C59D067.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458659111575, "dur": 654, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756458659112282, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1756458659112452, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756458659112589, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1756458659112887, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659113197, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1756458659113421, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756458659113620, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659114808, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.15f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 11, "ts": 1756458659113821, "dur": 4433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659118255, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659118667, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659119465, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659119675, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659119950, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659120280, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659120564, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659121002, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659121312, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659121758, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659122137, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659122527, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659122818, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659123156, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659123548, "dur": 62, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659123630, "dur": 889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659124519, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659125082, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458659125306, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458659125518, "dur": 1117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756458659126636, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659126911, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458659127150, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756458659127676, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659127817, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659127908, "dur": 732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659128662, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659128799, "dur": 1886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659130685, "dur": 1547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659132233, "dur": 62703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659194955, "dur": 6439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1756458659201395, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659201680, "dur": 5726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1756458659207407, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458659207535, "dur": 2987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1756458659210556, "dur": 51170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659087608, "dur": 22725, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659110334, "dur": 861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_D8ABA312F2231113.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458659111212, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_C70E350A8365786E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458659111414, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_EAB88A61CDB6E545.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458659111526, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458659111825, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756458659111957, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_33352EA77CF2B9C9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458659112113, "dur": 539, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1756458659112723, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756458659112816, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1756458659113205, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756458659113299, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756458659113445, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3489607026336439425.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756458659113620, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659113818, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659114128, "dur": 4589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659118717, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659119525, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659119812, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659120148, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659120523, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659120830, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659121204, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659121500, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659121839, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659122182, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659122461, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659122735, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659123056, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659123533, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659123632, "dur": 846, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659124525, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659125006, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458659125285, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659125502, "dur": 989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756458659126491, "dur": 650, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659127169, "dur": 605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659127774, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659127893, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659128641, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659128794, "dur": 1887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659130681, "dur": 1535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659132217, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458659132344, "dur": 62595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659194940, "dur": 5393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1756458659200371, "dur": 5366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1756458659205738, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659205816, "dur": 3842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1756458659209714, "dur": 808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458659210523, "dur": 51176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659087660, "dur": 22765, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659110428, "dur": 766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_76E612DE556D963F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458659111264, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659111358, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_7E22D088872CF900.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458659111552, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756458659111789, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1756458659111886, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_8ECF71F9F6689897.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458659112137, "dur": 214, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756458659112402, "dur": 293, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1756458659112723, "dur": 229, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756458659113046, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756458659113157, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659113415, "dur": 304, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756458659113744, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659113862, "dur": 3823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659117685, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659117878, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659118101, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659118328, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659118830, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659119596, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659119844, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659120316, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659120591, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659120874, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659121306, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659121622, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659121969, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659122253, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659122532, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659122871, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659123212, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659123602, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659123660, "dur": 856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659124516, "dur": 650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659125168, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458659125679, "dur": 1074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756458659126753, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659127055, "dur": 718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659127773, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659127888, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458659128065, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756458659128643, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": 1756458659129210, "dur": 138, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659129729, "dur": 58624, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": 1756458659194900, "dur": 2275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1756458659197176, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458659197252, "dur": 4861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1756458659202166, "dur": 5180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1756458659207387, "dur": 3089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1756458659210522, "dur": 51176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659087691, "dur": 22554, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659110247, "dur": 944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_113F580BF08E1069.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458659111451, "dur": 402, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1756458659111919, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_4A87A716FBA7BB4C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458659112208, "dur": 299, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756458659112585, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1756458659112813, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1756458659113048, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756458659113202, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1756458659113390, "dur": 340, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756458659113750, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659113899, "dur": 3306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659117206, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659117379, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659117551, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659117698, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659117874, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659118217, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659118394, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659118821, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659119040, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659119894, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659120238, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659120589, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659120883, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659121185, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659121510, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659121712, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659122137, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659122478, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659122939, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659123294, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659123542, "dur": 83, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659123626, "dur": 885, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659124512, "dur": 872, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659125390, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458659125733, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756458659126269, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659126511, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659126574, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659126963, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659127039, "dur": 727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659127767, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458659127965, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756458659128520, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659128650, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659128728, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458659128880, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756458659129206, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659129394, "dur": 1283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659130678, "dur": 1539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659132218, "dur": 62684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659194903, "dur": 3779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1756458659198730, "dur": 2925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1756458659201657, "dur": 2035, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659203740, "dur": 3607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1756458659207348, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458659207459, "dur": 3023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1756458659210537, "dur": 51188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659087717, "dur": 22574, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659110295, "dur": 918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_2C4CF458CCFD79E5.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458659111216, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659111328, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_2C4CF458CCFD79E5.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458659111405, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_43C4A2371BE27672.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458659111736, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659111803, "dur": 487, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_43C4A2371BE27672.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458659112355, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756458659112471, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659112577, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756458659112808, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1756458659113024, "dur": 378, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756458659113405, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756458659113538, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659113641, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756458659113742, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659114823, "dur": 521, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.15f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.DiaSymReader.Native.amd64.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756458659113990, "dur": 3870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659117860, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659118055, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659118269, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659118690, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659119016, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659119659, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659120299, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659120620, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659120875, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659121245, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659121583, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659121995, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659122401, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659122851, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659123218, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659123681, "dur": 855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659124537, "dur": 845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659125383, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458659125479, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659125581, "dur": 1288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756458659126870, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659127039, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659127575, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659127767, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659127896, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659128645, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659128756, "dur": 1918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659130674, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458659130764, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756458659131097, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659131181, "dur": 1059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659132240, "dur": 63004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659195246, "dur": 5006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1756458659200253, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659200658, "dur": 3554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1756458659204213, "dur": 407, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659204638, "dur": 3519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1756458659208158, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659208568, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659208773, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659208839, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659209117, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1756458659209191, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1756458659209266, "dur": 340, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1756458659209607, "dur": 772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458659210430, "dur": 51266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659087746, "dur": 22561, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659110308, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_0B4B7F18296A2318.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458659111076, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_E521CF7A8467AFE9.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458659111567, "dur": 1332, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_6FBDBF7066DCE3B7.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458659112945, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659113177, "dur": 256, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756458659113512, "dur": 226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756458659113739, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659114055, "dur": 4507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659118562, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659118852, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659119062, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659119831, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659120200, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659120455, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659120726, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659120911, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659121170, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659121515, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659121796, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659122087, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659122296, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659122685, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659123034, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659123416, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659124126, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659124501, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659125013, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458659125159, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756458659125929, "dur": 580, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659126571, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659126962, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1756458659127050, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659127776, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659127890, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458659128100, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756458659128667, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659128745, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659129421, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458659129541, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756458659130229, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458659130340, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756458659130821, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458659130937, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756458659131383, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458659131480, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756458659131770, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659132221, "dur": 62678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659194901, "dur": 2534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Unified.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1756458659197436, "dur": 2906, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659200350, "dur": 3277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1756458659203664, "dur": 2332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1756458659206025, "dur": 2679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1756458659208705, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659209228, "dur": 587, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659209822, "dur": 794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458659210638, "dur": 51082, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659087774, "dur": 22513, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659110291, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_FE5DA9A8F69DF848.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756458659111078, "dur": 318, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_07DABE08CBA915AD.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756458659111423, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659111571, "dur": 345, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1756458659111939, "dur": 327, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1756458659112412, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1756458659112592, "dur": 267, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1756458659112894, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659113163, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1756458659113498, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1756458659113608, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1756458659113694, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659113852, "dur": 4415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659118268, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659118568, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659118904, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659119772, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659120190, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659120627, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659120944, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659121394, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659121910, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659122292, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659122498, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659122801, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659123213, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659123452, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659124034, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659124510, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659125031, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756458659125243, "dur": 878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1756458659126122, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659126303, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756458659126652, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659126969, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1756458659127720, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659127899, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659128642, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659128759, "dur": 1916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659130675, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659130869, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756458659131065, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1756458659131414, "dur": 828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659132242, "dur": 62664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659194932, "dur": 2081, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1756458659197014, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659197256, "dur": 3901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1756458659201158, "dur": 600, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659201768, "dur": 3240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1756458659205009, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659205280, "dur": 3477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1756458659208757, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659208822, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1756458659209016, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659209205, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.dll"}}, {"pid": 12345, "tid": 17, "ts": 1756458659209256, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659209620, "dur": 892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458659210548, "dur": 51153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659087799, "dur": 22430, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659110230, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_ECC6BD7BBFF90F85.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458659110727, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_A320F650853E0906.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458659110894, "dur": 658, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_E9E3146384A07F6C.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458659111580, "dur": 517, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_CEF96CF5640B5F47.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458659112122, "dur": 754, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756458659113021, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756458659113166, "dur": 208, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756458659113432, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7043721260763788226.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756458659113511, "dur": 225, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7043721260763788226.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756458659113739, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659115029, "dur": 595, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.15f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.DataProtection.Extensions.dll"}}, {"pid": 12345, "tid": 18, "ts": 1756458659114068, "dur": 4817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659118886, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659119578, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659119795, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659120125, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659120511, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659121107, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659121509, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659121869, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659122240, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659122535, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659122959, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659123362, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659124071, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659124491, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659125001, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458659125354, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1756458659126549, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458659126829, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1756458659127467, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659127582, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659127760, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458659127985, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1756458659128509, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659128731, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458659128874, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1756458659129337, "dur": 1359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659130697, "dur": 1534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659132232, "dur": 62713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659194947, "dur": 8509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1756458659203457, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659203713, "dur": 5815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1756458659209609, "dur": 899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458659210526, "dur": 51178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659087828, "dur": 22389, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659110218, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_76639DDD4EDD4D5F.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458659110975, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_C8510191417D2A71.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458659111083, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659111171, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_6E4D82D58E0D5F0A.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458659111241, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_BCAB1FFD58D166C0.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458659111449, "dur": 728, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1756458659112255, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1756458659112370, "dur": 292, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756458659112716, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756458659113076, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659113192, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756458659113500, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8863518860715653438.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756458659113728, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659113940, "dur": 524, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.15f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 19, "ts": 1756458659114685, "dur": 574, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.15f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.HttpListener.dll"}}, {"pid": 12345, "tid": 19, "ts": 1756458659115260, "dur": 670, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.15f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.Http.Json.dll"}}, {"pid": 12345, "tid": 19, "ts": 1756458659116390, "dur": 578, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.15f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 19, "ts": 1756458659113846, "dur": 4752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659118598, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659119039, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Nodes\\Math\\Basic\\SubtractNode.cs"}}, {"pid": 12345, "tid": 19, "ts": 1756458659118943, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659119759, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659120147, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659120594, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659121071, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659121308, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659121674, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659122114, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659122521, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659123100, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659123238, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659123838, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659124507, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659125038, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458659125512, "dur": 1194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1756458659126750, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659126820, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659126963, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659127036, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659127287, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659127778, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659127891, "dur": 752, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659128643, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659128789, "dur": 1883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659130673, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458659130777, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1756458659131220, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458659131332, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1756458659131657, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458659131724, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1756458659131918, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659132247, "dur": 62669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659194924, "dur": 2792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1756458659197768, "dur": 5607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1756458659203377, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659203508, "dur": 5679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1756458659209188, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659209303, "dur": 943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659210250, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458659210490, "dur": 51197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659087848, "dur": 22427, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659110277, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1560055E669A2E68.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458659110892, "dur": 264, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1560055E669A2E68.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458659111166, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_C06DD15739D13551.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458659111584, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659111643, "dur": 205, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_C06DD15739D13551.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458659111866, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1756458659112045, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659112140, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1756458659112405, "dur": 347, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1756458659112756, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756458659112882, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659112944, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756458659113054, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756458659113406, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10411718816959651794.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756458659113634, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659114724, "dur": 672, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.15f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756458659116438, "dur": 680, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.15f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756458659113831, "dur": 3955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659117786, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659118004, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659118194, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659118406, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659118778, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659119597, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659120022, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659120412, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659120728, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659121090, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659121350, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659121760, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659122144, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659122445, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659123154, "dur": 113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659123268, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659123543, "dur": 69, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659123615, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659124540, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659125067, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458659125588, "dur": 2014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1756458659127642, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458659127800, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1756458659128272, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659128674, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659128809, "dur": 1884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659130694, "dur": 1529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659132223, "dur": 62710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659194939, "dur": 5687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1756458659200628, "dur": 814, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659201451, "dur": 6950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1756458659208402, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659208636, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659208860, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659208936, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mobile.AndroidLogcat.Editor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756458659209262, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659209618, "dur": 891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458659210535, "dur": 51153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458659270142, "dur": 1158, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 23380, "tid": 135, "ts": 1756458659273486, "dur": 388, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 23380, "tid": 135, "ts": 1756458659273900, "dur": 7640, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 23380, "tid": 135, "ts": 1756458659271792, "dur": 9800, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}