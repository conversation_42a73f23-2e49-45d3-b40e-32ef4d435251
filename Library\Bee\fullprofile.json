{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 23380, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 23380, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 23380, "tid": 117, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 23380, "tid": 117, "ts": 1756458037222220, "dur": 10, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 23380, "tid": 117, "ts": 1756458037222241, "dur": 5, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 23380, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 23380, "tid": 1, "ts": 1756458028356593, "dur": 1062, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 23380, "tid": 1, "ts": 1756458028357657, "dur": 21745, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 23380, "tid": 1, "ts": 1756458028379405, "dur": 25995, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 23380, "tid": 117, "ts": 1756458037222248, "dur": 10, "ph": "X", "name": "", "args": {}}, {"pid": 23380, "tid": 55834574848, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028356570, "dur": 15347, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028371919, "dur": 8849562, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028371930, "dur": 90, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028372024, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028372027, "dur": 244, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028372277, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028372329, "dur": 7, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028372337, "dur": 3383, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028375729, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028375732, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028375792, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028375794, "dur": 26, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028375823, "dur": 85, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028375911, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028375915, "dur": 33, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028375951, "dur": 2, "ph": "X", "name": "ProcessMessages 1482", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028375953, "dur": 36, "ph": "X", "name": "ReadAsync 1482", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028375992, "dur": 1, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028375995, "dur": 39, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376037, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376040, "dur": 40, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376082, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376084, "dur": 34, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376121, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376123, "dur": 55, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376181, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376212, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376213, "dur": 38, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376255, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376257, "dur": 36, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376295, "dur": 1, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376298, "dur": 26, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376327, "dur": 24, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376353, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376355, "dur": 29, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376386, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376387, "dur": 26, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376417, "dur": 28, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376448, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376449, "dur": 34, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376486, "dur": 1, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376488, "dur": 24, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376514, "dur": 42, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376559, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376561, "dur": 25, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376588, "dur": 24, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376613, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376615, "dur": 23, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376641, "dur": 29, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376673, "dur": 23, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376700, "dur": 20, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376722, "dur": 36, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376760, "dur": 23, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376786, "dur": 22, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376810, "dur": 18, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376831, "dur": 22, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376855, "dur": 23, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376880, "dur": 21, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376903, "dur": 23, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376929, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376951, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376953, "dur": 20, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376975, "dur": 21, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028376997, "dur": 20, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377019, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377022, "dur": 24, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377049, "dur": 21, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377072, "dur": 22, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377097, "dur": 25, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377126, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377161, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377163, "dur": 21, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377186, "dur": 20, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377210, "dur": 34, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377247, "dur": 22, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377271, "dur": 19, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377293, "dur": 37, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377331, "dur": 24, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377358, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377382, "dur": 17, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377401, "dur": 21, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377425, "dur": 20, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377447, "dur": 25, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377474, "dur": 22, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377497, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377499, "dur": 20, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377521, "dur": 23, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377546, "dur": 19, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377568, "dur": 20, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377589, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377591, "dur": 24, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377618, "dur": 21, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377641, "dur": 20, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377664, "dur": 22, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377688, "dur": 20, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377708, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377710, "dur": 25, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377737, "dur": 40, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377778, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377780, "dur": 22, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377805, "dur": 20, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377827, "dur": 20, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377849, "dur": 22, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377873, "dur": 20, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377895, "dur": 19, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377916, "dur": 25, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377943, "dur": 20, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377965, "dur": 21, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028377988, "dur": 22, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378012, "dur": 23, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378037, "dur": 18, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378057, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378079, "dur": 20, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378101, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378124, "dur": 28, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378154, "dur": 21, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378177, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378180, "dur": 53, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378242, "dur": 3, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378247, "dur": 75, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378325, "dur": 2, "ph": "X", "name": "ProcessMessages 986", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378329, "dur": 47, "ph": "X", "name": "ReadAsync 986", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378379, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378382, "dur": 33, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378417, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378418, "dur": 20, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378440, "dur": 88, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378530, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378582, "dur": 24, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378608, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378636, "dur": 20, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378657, "dur": 18, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378676, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378678, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378709, "dur": 25, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378736, "dur": 23, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378761, "dur": 42, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378807, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378809, "dur": 30, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378841, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378873, "dur": 24, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378899, "dur": 19, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378920, "dur": 20, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378942, "dur": 13, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378957, "dur": 17, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028378976, "dur": 22, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379000, "dur": 29, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379032, "dur": 34, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379069, "dur": 24, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379095, "dur": 21, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379119, "dur": 20, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379141, "dur": 18, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379161, "dur": 19, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379182, "dur": 20, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379203, "dur": 25, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379230, "dur": 25, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379258, "dur": 20, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379280, "dur": 17, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379299, "dur": 17, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379318, "dur": 23, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379343, "dur": 23, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379368, "dur": 21, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379392, "dur": 20, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379414, "dur": 20, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379436, "dur": 19, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379457, "dur": 17, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379475, "dur": 41, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379518, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379544, "dur": 28, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379574, "dur": 19, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379596, "dur": 32, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379631, "dur": 25, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379658, "dur": 22, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379683, "dur": 20, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379705, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379730, "dur": 20, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379753, "dur": 23, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379777, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379779, "dur": 27, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379808, "dur": 21, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379831, "dur": 15, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379847, "dur": 16, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379865, "dur": 24, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379892, "dur": 22, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379915, "dur": 19, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379937, "dur": 18, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379957, "dur": 29, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379990, "dur": 1, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028379992, "dur": 37, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380032, "dur": 20, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380054, "dur": 17, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380073, "dur": 18, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380093, "dur": 17, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380112, "dur": 18, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380131, "dur": 27, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380161, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380180, "dur": 17, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380198, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380221, "dur": 19, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380241, "dur": 54, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380298, "dur": 29, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380329, "dur": 16, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380347, "dur": 22, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380370, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380389, "dur": 22, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380414, "dur": 24, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380440, "dur": 43, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380486, "dur": 18, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380505, "dur": 19, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380527, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380529, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380551, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380575, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380577, "dur": 22, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380603, "dur": 19, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380625, "dur": 15, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380641, "dur": 20, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380663, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380697, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380699, "dur": 29, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380731, "dur": 22, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380755, "dur": 20, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380777, "dur": 51, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380831, "dur": 25, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380858, "dur": 23, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380884, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380916, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380918, "dur": 27, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380947, "dur": 34, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028380983, "dur": 22, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381008, "dur": 219, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381229, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381259, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381261, "dur": 19, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381282, "dur": 19, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381305, "dur": 25, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381332, "dur": 23, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381358, "dur": 22, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381384, "dur": 33, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381419, "dur": 23, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381444, "dur": 20, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381466, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381488, "dur": 21, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381510, "dur": 19, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381531, "dur": 20, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381552, "dur": 25, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381580, "dur": 25, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381611, "dur": 35, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381648, "dur": 20, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381670, "dur": 18, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381689, "dur": 21, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381712, "dur": 22, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381735, "dur": 21, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381758, "dur": 20, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381780, "dur": 19, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381801, "dur": 23, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381828, "dur": 32, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381861, "dur": 1, "ph": "X", "name": "ProcessMessages 1028", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381862, "dur": 22, "ph": "X", "name": "ReadAsync 1028", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381887, "dur": 22, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381911, "dur": 17, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381929, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381947, "dur": 19, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381969, "dur": 20, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028381991, "dur": 27, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382018, "dur": 8, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382027, "dur": 18, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382047, "dur": 90, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382138, "dur": 38, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382178, "dur": 1, "ph": "X", "name": "ProcessMessages 2223", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382180, "dur": 24, "ph": "X", "name": "ReadAsync 2223", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382205, "dur": 21, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382228, "dur": 24, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382255, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382257, "dur": 45, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382305, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382307, "dur": 26, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382336, "dur": 23, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382360, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382383, "dur": 25, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382410, "dur": 20, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382471, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382473, "dur": 25, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382503, "dur": 21, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382529, "dur": 18, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382548, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382571, "dur": 19, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382593, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382615, "dur": 19, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382635, "dur": 35, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382675, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382677, "dur": 35, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382714, "dur": 1, "ph": "X", "name": "ProcessMessages 1137", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382715, "dur": 26, "ph": "X", "name": "ReadAsync 1137", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382743, "dur": 19, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382764, "dur": 24, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382790, "dur": 22, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382814, "dur": 23, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382841, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382843, "dur": 24, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382868, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382893, "dur": 34, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028382976, "dur": 32, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383010, "dur": 1, "ph": "X", "name": "ProcessMessages 1280", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383011, "dur": 24, "ph": "X", "name": "ReadAsync 1280", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383037, "dur": 31, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383071, "dur": 80, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383152, "dur": 1, "ph": "X", "name": "ProcessMessages 1324", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383154, "dur": 35, "ph": "X", "name": "ReadAsync 1324", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383191, "dur": 23, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383215, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383238, "dur": 16, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383256, "dur": 19, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383276, "dur": 25, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383303, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383329, "dur": 20, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383351, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383373, "dur": 20, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383395, "dur": 63, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383461, "dur": 40, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383503, "dur": 32, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383537, "dur": 1, "ph": "X", "name": "ProcessMessages 1820", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383592, "dur": 43, "ph": "X", "name": "ReadAsync 1820", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383636, "dur": 1, "ph": "X", "name": "ProcessMessages 1347", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383638, "dur": 26, "ph": "X", "name": "ReadAsync 1347", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383666, "dur": 64, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383732, "dur": 26, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383761, "dur": 35, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383798, "dur": 16, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383817, "dur": 20, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383838, "dur": 17, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383856, "dur": 16, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383874, "dur": 19, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383895, "dur": 16, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383914, "dur": 18, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383933, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383934, "dur": 22, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383958, "dur": 22, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383981, "dur": 15, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028383998, "dur": 16, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384016, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384045, "dur": 19, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384066, "dur": 17, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384085, "dur": 24, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384112, "dur": 37, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384151, "dur": 37, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384191, "dur": 24, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384217, "dur": 35, "ph": "X", "name": "ReadAsync 1080", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384254, "dur": 65, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384322, "dur": 2, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384325, "dur": 51, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384377, "dur": 1, "ph": "X", "name": "ProcessMessages 1793", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384378, "dur": 28, "ph": "X", "name": "ReadAsync 1793", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384409, "dur": 27, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384440, "dur": 47, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384489, "dur": 41, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384532, "dur": 96, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384630, "dur": 23, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384655, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384658, "dur": 42, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384702, "dur": 28, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384732, "dur": 1, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384734, "dur": 20, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384759, "dur": 40, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384801, "dur": 33, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384836, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384862, "dur": 33, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384898, "dur": 1, "ph": "X", "name": "ProcessMessages 1219", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384900, "dur": 36, "ph": "X", "name": "ReadAsync 1219", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384984, "dur": 1, "ph": "X", "name": "ProcessMessages 978", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028384986, "dur": 26, "ph": "X", "name": "ReadAsync 978", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385013, "dur": 1, "ph": "X", "name": "ProcessMessages 1336", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385015, "dur": 18, "ph": "X", "name": "ReadAsync 1336", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385035, "dur": 18, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385056, "dur": 20, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385078, "dur": 19, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385098, "dur": 20, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385120, "dur": 26, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385148, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385184, "dur": 32, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385217, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385218, "dur": 19, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385239, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385241, "dur": 20, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385263, "dur": 22, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385287, "dur": 19, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385309, "dur": 30, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385341, "dur": 20, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385364, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385389, "dur": 16, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385407, "dur": 18, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385426, "dur": 20, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385448, "dur": 19, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385469, "dur": 20, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385492, "dur": 18, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385511, "dur": 13, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385525, "dur": 20, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385547, "dur": 33, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385583, "dur": 19, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385603, "dur": 23, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385628, "dur": 17, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385648, "dur": 18, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385669, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385692, "dur": 19, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385712, "dur": 19, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385732, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385734, "dur": 18, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385753, "dur": 15, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385770, "dur": 19, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385791, "dur": 20, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385814, "dur": 20, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385836, "dur": 19, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385857, "dur": 18, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385877, "dur": 21, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385899, "dur": 20, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385922, "dur": 19, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385942, "dur": 20, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385965, "dur": 30, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028385997, "dur": 18, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386016, "dur": 18, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386039, "dur": 21, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386063, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386085, "dur": 21, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386109, "dur": 29, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386140, "dur": 33, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386175, "dur": 20, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386197, "dur": 20, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386219, "dur": 20, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386241, "dur": 36, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386279, "dur": 15, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386297, "dur": 23, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386321, "dur": 19, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386342, "dur": 20, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386365, "dur": 20, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386386, "dur": 17, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386405, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386406, "dur": 17, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386425, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386448, "dur": 19, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386470, "dur": 19, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386491, "dur": 22, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386515, "dur": 22, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386539, "dur": 17, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386558, "dur": 17, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386577, "dur": 20, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386599, "dur": 19, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386619, "dur": 17, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386638, "dur": 17, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386656, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386678, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386701, "dur": 20, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386723, "dur": 20, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386744, "dur": 23, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386769, "dur": 19, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386790, "dur": 15, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386807, "dur": 17, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386827, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386849, "dur": 20, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386871, "dur": 19, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386892, "dur": 21, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386915, "dur": 21, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386939, "dur": 22, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386963, "dur": 19, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028386983, "dur": 20, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387006, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387028, "dur": 32, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387062, "dur": 29, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387094, "dur": 20, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387116, "dur": 19, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387137, "dur": 21, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387159, "dur": 19, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387181, "dur": 22, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387205, "dur": 20, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387227, "dur": 19, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387248, "dur": 19, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387269, "dur": 18, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387289, "dur": 18, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387310, "dur": 17, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387330, "dur": 21, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387353, "dur": 27, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387382, "dur": 15, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387398, "dur": 11, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387410, "dur": 15, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387427, "dur": 23, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387452, "dur": 20, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387474, "dur": 21, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387498, "dur": 17, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387517, "dur": 20, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387539, "dur": 17, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387558, "dur": 23, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387583, "dur": 16, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387601, "dur": 25, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387629, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387651, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387675, "dur": 18, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387696, "dur": 58, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387756, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387779, "dur": 20, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387801, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387803, "dur": 16, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387821, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387883, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387908, "dur": 19, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387930, "dur": 17, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387948, "dur": 17, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028387967, "dur": 75, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388045, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388094, "dur": 24, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388121, "dur": 18, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388176, "dur": 35, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388214, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388239, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388263, "dur": 18, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388284, "dur": 61, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388350, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388381, "dur": 24, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388407, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388409, "dur": 59, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388470, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388557, "dur": 1, "ph": "X", "name": "ProcessMessages 1144", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388558, "dur": 15, "ph": "X", "name": "ReadAsync 1144", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388575, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388598, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388622, "dur": 63, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388687, "dur": 19, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388709, "dur": 19, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388730, "dur": 17, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388748, "dur": 18, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388768, "dur": 22, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388792, "dur": 13, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388807, "dur": 67, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388876, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388897, "dur": 26, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388925, "dur": 21, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028388950, "dur": 54, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389005, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389028, "dur": 21, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389052, "dur": 19, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389073, "dur": 54, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389129, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389154, "dur": 20, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389176, "dur": 18, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389196, "dur": 55, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389253, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389275, "dur": 17, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389294, "dur": 17, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389312, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389314, "dur": 50, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389367, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389389, "dur": 19, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389410, "dur": 17, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389429, "dur": 16, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389447, "dur": 51, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389499, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389523, "dur": 19, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389544, "dur": 18, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389564, "dur": 63, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389629, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389653, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389655, "dur": 18, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389675, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389676, "dur": 16, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389694, "dur": 51, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389747, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389777, "dur": 19, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389798, "dur": 45, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389845, "dur": 15, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389862, "dur": 39, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389903, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389927, "dur": 18, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389948, "dur": 18, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028389969, "dur": 67, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390038, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390061, "dur": 19, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390082, "dur": 17, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390101, "dur": 52, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390155, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390178, "dur": 24, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390204, "dur": 18, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390224, "dur": 48, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390274, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390299, "dur": 21, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390322, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390343, "dur": 21, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390366, "dur": 18, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390386, "dur": 56, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390444, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390524, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390526, "dur": 26, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390555, "dur": 23, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390579, "dur": 19, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390600, "dur": 239, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390843, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390844, "dur": 31, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390877, "dur": 1, "ph": "X", "name": "ProcessMessages 1354", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390878, "dur": 22, "ph": "X", "name": "ReadAsync 1354", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390904, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390935, "dur": 24, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390961, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390963, "dur": 25, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028390991, "dur": 21, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391015, "dur": 20, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391037, "dur": 17, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391056, "dur": 17, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391074, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391136, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391180, "dur": 26, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391209, "dur": 50, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391261, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391283, "dur": 17, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391303, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391322, "dur": 24, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391348, "dur": 16, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391366, "dur": 39, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391407, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391452, "dur": 20, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391474, "dur": 15, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391491, "dur": 16, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391509, "dur": 17, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391528, "dur": 63, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391593, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391620, "dur": 22, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391645, "dur": 17, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391664, "dur": 62, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391730, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391755, "dur": 27, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391786, "dur": 26, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391816, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391817, "dur": 44, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391865, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391894, "dur": 24, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391920, "dur": 18, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391940, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028391942, "dur": 56, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392000, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392021, "dur": 28, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392051, "dur": 23, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392077, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392078, "dur": 58, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392139, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392166, "dur": 19, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392186, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392210, "dur": 21, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392233, "dur": 20, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392255, "dur": 27, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392284, "dur": 27, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392314, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392343, "dur": 51, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392396, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392420, "dur": 20, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392442, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392466, "dur": 63, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392530, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392556, "dur": 34, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392591, "dur": 19, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392612, "dur": 72, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392689, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392745, "dur": 16, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392764, "dur": 23, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392789, "dur": 16, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392807, "dur": 60, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392869, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392890, "dur": 22, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392916, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392918, "dur": 21, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392940, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028392942, "dur": 60, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393005, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393035, "dur": 22, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393059, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393061, "dur": 61, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393124, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393147, "dur": 20, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393172, "dur": 20, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393194, "dur": 66, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393262, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393286, "dur": 19, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393307, "dur": 19, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393329, "dur": 62, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393393, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393413, "dur": 19, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393436, "dur": 22, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393460, "dur": 64, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393527, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393559, "dur": 22, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393583, "dur": 56, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393641, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393683, "dur": 20, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393705, "dur": 18, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393725, "dur": 52, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393779, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393801, "dur": 20, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393824, "dur": 17, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393844, "dur": 64, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393910, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393934, "dur": 20, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393956, "dur": 20, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028393979, "dur": 56, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394037, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394060, "dur": 15, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394078, "dur": 18, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394098, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394152, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394177, "dur": 22, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394201, "dur": 18, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394221, "dur": 18, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394241, "dur": 15, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394258, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394342, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394368, "dur": 21, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394392, "dur": 19, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394413, "dur": 71, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394486, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394509, "dur": 21, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394532, "dur": 15, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394549, "dur": 44, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394595, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394620, "dur": 20, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394642, "dur": 17, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394661, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394715, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394739, "dur": 19, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394760, "dur": 17, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394780, "dur": 60, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394841, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394871, "dur": 21, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394895, "dur": 21, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394918, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394935, "dur": 50, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028394988, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395011, "dur": 24, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395037, "dur": 16, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395056, "dur": 59, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395117, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395141, "dur": 21, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395164, "dur": 57, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395223, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395248, "dur": 23, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395273, "dur": 19, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395294, "dur": 55, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395351, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395373, "dur": 18, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395437, "dur": 18, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395459, "dur": 21, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395482, "dur": 20, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395504, "dur": 19, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395525, "dur": 72, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395598, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395624, "dur": 21, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395647, "dur": 19, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395668, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395728, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395751, "dur": 22, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395775, "dur": 15, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395792, "dur": 53, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395848, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395879, "dur": 36, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395917, "dur": 17, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028395937, "dur": 70, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396011, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396012, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396060, "dur": 1, "ph": "X", "name": "ProcessMessages 1222", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396062, "dur": 44, "ph": "X", "name": "ReadAsync 1222", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396108, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396137, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396139, "dur": 22, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396163, "dur": 21, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396187, "dur": 19, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396208, "dur": 16, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396227, "dur": 20, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396251, "dur": 16, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396269, "dur": 17, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396288, "dur": 57, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396350, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396372, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396374, "dur": 32, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396409, "dur": 17, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396428, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396453, "dur": 25, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396481, "dur": 52, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396535, "dur": 24, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396563, "dur": 22, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396587, "dur": 26, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396616, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396637, "dur": 83, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396722, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396790, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396794, "dur": 33, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396828, "dur": 1, "ph": "X", "name": "ProcessMessages 1513", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396829, "dur": 22, "ph": "X", "name": "ReadAsync 1513", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396853, "dur": 121, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396978, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028396980, "dur": 31, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397014, "dur": 101, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397120, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397153, "dur": 134, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397291, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397328, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397330, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397356, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397388, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397418, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397443, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397482, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397517, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397519, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397544, "dur": 20, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397567, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397593, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397595, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397617, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397639, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397659, "dur": 35, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397696, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397726, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397727, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397776, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397799, "dur": 51, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397852, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397872, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397887, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397906, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397926, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397945, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397969, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028397993, "dur": 27, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398024, "dur": 20, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398046, "dur": 18, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398066, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398088, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398134, "dur": 24, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398162, "dur": 4, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398168, "dur": 29, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398198, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398200, "dur": 20, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398222, "dur": 20, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398245, "dur": 15, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398263, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398288, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398290, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398322, "dur": 24, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398348, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398349, "dur": 19, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398372, "dur": 26, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398400, "dur": 19, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398422, "dur": 18, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398441, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398465, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398489, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398490, "dur": 26, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398519, "dur": 33, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398555, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398557, "dur": 31, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398590, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398591, "dur": 26, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398619, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398621, "dur": 26, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398648, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398649, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398676, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398708, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398733, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398735, "dur": 23, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398760, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398762, "dur": 19, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398783, "dur": 24, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398810, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398838, "dur": 53, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398893, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398894, "dur": 16, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398914, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028398943, "dur": 73, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399018, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399020, "dur": 25, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399047, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399049, "dur": 17, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399070, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399109, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399131, "dur": 16, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399149, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399168, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399199, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399222, "dur": 17, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399241, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399267, "dur": 43, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399311, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399348, "dur": 22, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399373, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399375, "dur": 364, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399742, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028399769, "dur": 5841, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028405618, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028405620, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028405648, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028405649, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028405667, "dur": 976, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028406646, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028406648, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028406667, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028406689, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028406711, "dur": 297, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028407010, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028407039, "dur": 2347, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028409398, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028409401, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028409465, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028409467, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028409516, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028409518, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028409565, "dur": 212, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028409780, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028409782, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028409826, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028409828, "dur": 167, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028409997, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410022, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410040, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410129, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410149, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410169, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410187, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410242, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410275, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410278, "dur": 56, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410336, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410360, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410378, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410454, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410476, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410521, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410542, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410629, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410656, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410675, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410692, "dur": 257, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410951, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028410975, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411088, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411116, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411118, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411142, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411217, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411240, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411263, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411309, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411333, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411361, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411384, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411497, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411516, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411591, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411652, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411678, "dur": 17, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411698, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411719, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411760, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411781, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411809, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411838, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411864, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411891, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411921, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411924, "dur": 22, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411948, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028411978, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412004, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412040, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412064, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412106, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412128, "dur": 67, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412197, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412220, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412258, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412286, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412312, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412329, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412358, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412387, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412389, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412416, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412440, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412442, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412586, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412613, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412639, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412666, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412689, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412720, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412748, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412861, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412886, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412912, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412913, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412955, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028412976, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413036, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413058, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413082, "dur": 158, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413244, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413273, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413300, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413303, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413340, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413372, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413374, "dur": 81, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413457, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413459, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413485, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413512, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413513, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413535, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413549, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413568, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413600, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413634, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413636, "dur": 280, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413919, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028413945, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414001, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414033, "dur": 139, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414175, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414178, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414201, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414239, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414263, "dur": 3, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414266, "dur": 296, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414567, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414568, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414598, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414600, "dur": 18, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414620, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414636, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414706, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414728, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414757, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414761, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414793, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414903, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028414929, "dur": 124, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415056, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415083, "dur": 85, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415170, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415197, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415228, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415249, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415367, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415392, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415479, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415514, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415516, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415554, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415556, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415602, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415673, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415697, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415726, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415751, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415816, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415839, "dur": 104, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415945, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415948, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028415968, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416035, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416059, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416084, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416109, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416131, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416157, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416188, "dur": 153, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416343, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416367, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416395, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416420, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416437, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416454, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416474, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416532, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416556, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416622, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416645, "dur": 224, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416872, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416901, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028416903, "dur": 106, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028417011, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028417035, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028417036, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028417061, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028417173, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028417192, "dur": 198, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028417393, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028417414, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028417417, "dur": 49170, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028466600, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028466603, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028466657, "dur": 19, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028466676, "dur": 8605, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028475294, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028475299, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028475349, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028475443, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028475475, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028475558, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028475560, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028475602, "dur": 286, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028475892, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028475910, "dur": 690, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028476606, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028476640, "dur": 605, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028477249, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028477251, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028477286, "dur": 286, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028477576, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028477612, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028477614, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028477649, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028477673, "dur": 186, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028477861, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028477883, "dur": 497, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028478384, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028478407, "dur": 349, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028478759, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028478779, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028478907, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028478909, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028478936, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028478938, "dur": 381, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028479322, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028479344, "dur": 253, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028479600, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028479623, "dur": 202, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028479829, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028479855, "dur": 421, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028480280, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028480311, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028480366, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028480391, "dur": 290, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028480683, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028480706, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028480726, "dur": 316, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028481048, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028481050, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028481072, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028481152, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028481176, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028481222, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028481224, "dur": 232, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028481460, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028481492, "dur": 212, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028481707, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028481731, "dur": 633, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028482369, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028482394, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028482476, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028482495, "dur": 591, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028483092, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028483105, "dur": 473, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028483582, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028483584, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028483610, "dur": 573, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028484187, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028484203, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028484273, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028484291, "dur": 312, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028484609, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028484611, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028484639, "dur": 168, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028484810, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028484841, "dur": 268, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028485112, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028485148, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028485149, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028485173, "dur": 179, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028485355, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028485388, "dur": 574, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028485969, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486009, "dur": 160, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486174, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486206, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486208, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486240, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486275, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486304, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486330, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486348, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486375, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486399, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486423, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486446, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486480, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486504, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486505, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486544, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486573, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486596, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486615, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486635, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486657, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486677, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486697, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486732, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486762, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486784, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486805, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486825, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486843, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486862, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486884, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486912, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486933, "dur": 13, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486948, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486968, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486969, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028486993, "dur": 18, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028487012, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028487035, "dur": 16, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028487054, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028487079, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028487124, "dur": 59, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028487186, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028487217, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028487245, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028487287, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028487310, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028487364, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028487420, "dur": 450, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028487872, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028487874, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028487916, "dur": 75, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028487997, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488035, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488062, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488084, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488117, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488144, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488145, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488172, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488212, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488235, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488256, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488288, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488328, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488352, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488373, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488447, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488462, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488487, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488489, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488521, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488552, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458028488579, "dur": 8718628, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458037207223, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458037207227, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458037207298, "dur": 26, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458037207325, "dur": 6193, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458037213526, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458037213556, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 23380, "tid": 55834574848, "ts": 1756458037213559, "dur": 7913, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 23380, "tid": 117, "ts": 1756458037222260, "dur": 1729, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 23380, "tid": 51539607552, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 23380, "tid": 51539607552, "ts": 1756458028356482, "dur": 48941, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 23380, "tid": 51539607552, "ts": 1756458028405425, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 23380, "tid": 51539607552, "ts": 1756458028405427, "dur": 60, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 23380, "tid": 117, "ts": 1756458037223991, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 23380, "tid": 47244640256, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 23380, "tid": 47244640256, "ts": 1756458028353437, "dur": 8868093, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 23380, "tid": 47244640256, "ts": 1756458028353559, "dur": 2897, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 23380, "tid": 47244640256, "ts": 1756458037221535, "dur": 42, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 23380, "tid": 47244640256, "ts": 1756458037221546, "dur": 18, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 23380, "tid": 117, "ts": 1756458037223997, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1756458028373587, "dur": 60, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458028373668, "dur": 1864, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458028375540, "dur": 780, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458028376446, "dur": 96, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1756458028376542, "dur": 861, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458028383917, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1756458028392226, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1756458028394460, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1756458028398149, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1756458028398228, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756458028377434, "dur": 21320, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458028398767, "dur": 8813919, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458037212688, "dur": 96, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458037212784, "dur": 62, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458037212847, "dur": 89, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458037215181, "dur": 67, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458037215272, "dur": 1547, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1756458028377632, "dur": 21161, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028398810, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028398881, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_A4DE870A30755667.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458028399245, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A143A3DB2F32642B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458028399390, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_07DABE08CBA915AD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458028399485, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_9D2B67F1A653618B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458028399748, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_A3FCC5129F44B4D8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458028400258, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1756458028400530, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1756458028400677, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756458028400845, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756458028400918, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756458028401026, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13408987163503134604.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756458028401105, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028401700, "dur": 3131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028404831, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028405335, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Graphs\\Vector3MaterialSlot.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756458028405284, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028406234, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028406627, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028406991, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028407381, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028407658, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028408000, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028408204, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028408520, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028408858, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028409164, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028409499, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028409773, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028410003, "dur": 52, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028410121, "dur": 1043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028411165, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028411600, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458028411965, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028412213, "dur": 1294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756458028413508, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028413696, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756458028414076, "dur": 868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756458028415036, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028415251, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028415811, "dur": 998, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028416810, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028417173, "dur": 1512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028418685, "dur": 56142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028474830, "dur": 2097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756458028476928, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028477001, "dur": 5652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756458028482654, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028482947, "dur": 5283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756458028488231, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028488459, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028488826, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028489046, "dur": 972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756458028490031, "dur": 8722691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028377632, "dur": 21147, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028398806, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028398876, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_E57B6CA5B0D6757A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458028399212, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_E57B6CA5B0D6757A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458028399286, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_2FA0746BBE576719.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458028399354, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028399427, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_697D3802CF3E27D6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458028399582, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_697D3802CF3E27D6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458028399750, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1756458028400004, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1756458028400112, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756458028400259, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1756458028400492, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1756458028400768, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756458028401010, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16659839770564295142.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756458028401110, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028401303, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16659839770564295142.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756458028401366, "dur": 2452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028403819, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028404000, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028404194, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028404384, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028404645, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028405122, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028406054, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028406362, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028406880, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028407406, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028407575, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028407903, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028408149, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028408394, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028408688, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028409012, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028409355, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028409794, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028410016, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028410246, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028411173, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028411589, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458028411757, "dur": 958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756458028412715, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028412868, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028413006, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028413435, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028413556, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028413736, "dur": 701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028414437, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028414532, "dur": 710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028415243, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028415695, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458028415801, "dur": 814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756458028416722, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028417163, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756458028417278, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756458028417594, "dur": 1093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028418687, "dur": 56150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028474866, "dur": 4455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756458028479361, "dur": 4837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756458028484231, "dur": 3573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756458028487805, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756458028487929, "dur": 2259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756458028490235, "dur": 8722583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028377853, "dur": 21102, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028398956, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_2C4CF458CCFD79E5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458028399246, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_715C3E420D54DFA3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458028399330, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_7D653DAE049DA23B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458028399382, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028399447, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_7D653DAE049DA23B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458028399529, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_490D37C9590BB1EA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458028399836, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458028400015, "dur": 438, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1756458028400461, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1756458028400611, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756458028400674, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756458028400990, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756458028401083, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028401439, "dur": 2938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028404378, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028404626, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028405230, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028406186, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028406592, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028407015, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028407522, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028407797, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028408167, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028408508, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028408727, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028409107, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028409373, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028409760, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028410129, "dur": 1040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028411169, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028411597, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458028411916, "dur": 865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756458028412781, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028412876, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458028412977, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458028413471, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756458028414197, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028414448, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028414526, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028414722, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028414823, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028415250, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028415794, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756458028415952, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756458028416342, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028416749, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028417169, "dur": 1511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028418681, "dur": 56137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028474821, "dur": 4335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756458028479157, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028479322, "dur": 3861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756458028483218, "dur": 3269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756458028486488, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756458028486568, "dur": 3262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756458028489868, "dur": 8722815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028377842, "dur": 21008, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028398851, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_24A430C27796CDE1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458028399264, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_8A797B0F0CB1BAE9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458028399333, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_69C0C0E5D468F8D0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458028399414, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_9ED85F7723A041B8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458028399566, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_9ED85F7723A041B8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458028399633, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_F70218AE2376F572.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458028399820, "dur": 409, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1756458028400232, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458028400311, "dur": 6955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756458028407267, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028407421, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028407618, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028407838, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028408133, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028408364, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028408638, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028409179, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028409549, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028409707, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028410029, "dur": 123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028410152, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028411192, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028411724, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458028412019, "dur": 1042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756458028413061, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028413458, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028413551, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028413738, "dur": 696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028414434, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458028414637, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756458028415119, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028415254, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028415801, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028416716, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458028416827, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756458028417325, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458028417432, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756458028417806, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756458028417910, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756458028418159, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028418702, "dur": 56159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028474862, "dur": 2783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1756458028477645, "dur": 703, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028478354, "dur": 4093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.iOS.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1756458028482448, "dur": 461, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028482917, "dur": 2953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1756458028485871, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756458028485948, "dur": 3774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1756458028489779, "dur": 8722974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458028377831, "dur": 21007, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458028398911, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_067C0613C0E0DB9E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458028399039, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_067C0613C0E0DB9E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458028399107, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_AFF857DF058FD9A1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458028399220, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_F6CB7DECB5054A4C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458028399427, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_644596ED4FF596EA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458028399595, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_644596ED4FF596EA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458028399774, "dur": 363, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A0B1B49A6ED86309.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458028400200, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458028400294, "dur": 8062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756458028408438, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458028408773, "dur": 2307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756458028411210, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458028411276, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756458028411602, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458028411757, "dur": 953, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458028412715, "dur": 948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756458028413736, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458028413882, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756458028414437, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756458028414616, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756458028415256, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1756458028415575, "dur": 71, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458028416026, "dur": 52236, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1756458028474831, "dur": 2334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1756458028477166, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458028477223, "dur": 2848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1756458028480072, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458028480140, "dur": 2606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1756458028482802, "dur": 3167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1756458028485970, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458028486037, "dur": 3389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1756458028489426, "dur": 503, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756458028489957, "dur": 8722722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028377677, "dur": 21125, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028398809, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_3C8F4B722C6531CA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458028399189, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_E0F443DE57A60521.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458028399273, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_E0F443DE57A60521.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458028399484, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_F74E89BA682F97FF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458028399677, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_3B9BDFCC211E9D6D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458028399925, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1756458028400250, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1756458028400432, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1756458028400647, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756458028400785, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756458028401103, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028401580, "dur": 2317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028403897, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028404030, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028404201, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028404427, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028404858, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028405926, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028406183, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028406710, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028407030, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028407438, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028407972, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028408265, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028408518, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028408840, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028409157, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028409496, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028409736, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028410023, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028410114, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028410275, "dur": 907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028411183, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028411600, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458028411921, "dur": 1468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756458028413390, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028413453, "dur": 554, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028414012, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028414503, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458028414800, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756458028415281, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028415802, "dur": 916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028416718, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028416846, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756458028416985, "dur": 1176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756458028418210, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028418697, "dur": 56158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028474857, "dur": 5777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1756458028480671, "dur": 8049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1756458028488838, "dur": 789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028489658, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756458028490232, "dur": 8722458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028377773, "dur": 21050, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028398834, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_7A4BC2D416416A7B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458028399112, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_2403B35E96799645.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458028399439, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_9D804A336986938E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458028399526, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_EF2C1B44647AC877.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458028399746, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_EF2C1B44647AC877.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458028399901, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458028400022, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1756458028400157, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458028400289, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458028400432, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458028400672, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458028400798, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458028401000, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756458028401101, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028401312, "dur": 3131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028404443, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028404713, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028405817, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028406228, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028406407, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028406606, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028406902, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028407425, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028407888, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028408126, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028408365, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028408903, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028409132, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028409674, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028409931, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028410677, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028411196, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028411726, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756458028412208, "dur": 1359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756458028413608, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028413676, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028413746, "dur": 724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028414470, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028414542, "dur": 698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028415264, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028415826, "dur": 932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028416758, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028417171, "dur": 1518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028418689, "dur": 56155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028474846, "dur": 4720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1756458028479624, "dur": 3227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1756458028482852, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028482922, "dur": 5693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1756458028488615, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028488842, "dur": 922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028489764, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756458028490303, "dur": 8722397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028377737, "dur": 21076, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028398820, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_C79C35D09E315F10.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458028399338, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028399553, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_3CF76F5C2290DA72.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458028399742, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_43C4A2371BE27672.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458028400060, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756458028400344, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756458028400456, "dur": 297, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1756458028400789, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756458028400942, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756458028401056, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756458028401151, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028401348, "dur": 2984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028404333, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028404602, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028404804, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028405209, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028406151, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028406643, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028407019, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028407354, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028407678, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028408173, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028408470, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028408763, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028409183, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028409556, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028409813, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028410168, "dur": 1009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028411177, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756458028411582, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458028411998, "dur": 2389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756458028414500, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458028414722, "dur": 917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756458028415691, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458028415814, "dur": 830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756458028416719, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458028416817, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756458028417170, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458028417293, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756458028417822, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458028417926, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756458028418227, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458028418295, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756458028418693, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756458028418772, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756458028418936, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756458028419217, "dur": 8789105, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756458028377881, "dur": 21125, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028399124, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_8AEC105970967529.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458028399284, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_5860F673E9B806F3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458028399362, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_5860F673E9B806F3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458028399459, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_C70E350A8365786E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458028399611, "dur": 294, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_C70E350A8365786E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458028399925, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1756458028400047, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1756458028400162, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1756458028400332, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1756458028400490, "dur": 379, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1756458028401006, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756458028401130, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028401360, "dur": 2214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028403575, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028404298, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028404492, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028405336, "dur": 523, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Nodes\\UV\\PolarCoordinatesNode.cs"}}, {"pid": 12345, "tid": 9, "ts": 1756458028404863, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028406008, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028406452, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028406701, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028407105, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028407470, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028407873, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028408106, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028408549, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028409099, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028409390, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028409715, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028410123, "dur": 1039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028411187, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028411615, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458028412092, "dur": 1223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756458028413353, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756458028413466, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028413577, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028413734, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458028414020, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756458028414719, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028415248, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028415802, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458028415995, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756458028416436, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028416521, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028416732, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028417163, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028417327, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756458028417481, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756458028417869, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028418695, "dur": 58491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028477187, "dur": 5452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1756458028482640, "dur": 824, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028483470, "dur": 4475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1756458028488040, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028488205, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028488581, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028488835, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028489142, "dur": 963, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756458028490125, "dur": 8722568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028377918, "dur": 21067, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028398985, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_0B4B7F18296A2318.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458028399100, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_A320F650853E0906.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458028399263, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_413D72FD9D818F92.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458028399339, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D30F86F3A61014E6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458028399390, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028399609, "dur": 258, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_166FE659F547CA5D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458028399930, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756458028400138, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756458028400234, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756458028400431, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756458028400609, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756458028400671, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756458028400776, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028400866, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756458028400989, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10131411833777065430.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756458028401058, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756458028401118, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028401340, "dur": 2511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028403851, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028404015, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028404212, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028404375, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028404622, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028405231, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028406164, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028406483, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028406946, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028407367, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028407935, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028408609, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028408995, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028409266, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028409671, "dur": 83, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028409754, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028410002, "dur": 95, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028410116, "dur": 1047, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028411163, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028411589, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458028411790, "dur": 1209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1756458028413000, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028413128, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028413434, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028413550, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028413737, "dur": 700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028414437, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028414502, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458028414841, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028415329, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1756458028415811, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458028415933, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1756458028416387, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458028416466, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1756458028416842, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756458028416928, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1756458028417252, "dur": 1438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028418690, "dur": 56140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028474831, "dur": 2107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1756458028476986, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1756458028477195, "dur": 4305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1756458028481501, "dur": 531, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028482044, "dur": 5589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1756458028487634, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756458028487721, "dur": 2532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1756458028490301, "dur": 8722397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028377941, "dur": 21026, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028398968, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_414CE17575401FA5.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458028399053, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_2C44277543DF4EF4.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458028399182, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_2C44277543DF4EF4.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458028399327, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_7440D828B24F1399.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458028399556, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_7440D828B24F1399.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458028399622, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_6C0F24EDA32924AF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458028399726, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_40431BCF65CB6FB0.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458028399951, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756458028400105, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756458028400382, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756458028400629, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756458028400797, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756458028401096, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028401441, "dur": 2997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028404438, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028404722, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028405317, "dur": 509, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Nodes\\Input\\Geometry\\UVNode.cs"}}, {"pid": 12345, "tid": 11, "ts": 1756458028405176, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028406063, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028406464, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028406819, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028406990, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028407390, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028407621, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028407875, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028408230, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028409024, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028409383, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028409609, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028409943, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028410529, "dur": 662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028411191, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028411729, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458028412021, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028412442, "dur": 1245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756458028413740, "dur": 696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028414436, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028414501, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756458028414801, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756458028415259, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028415807, "dur": 996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028416804, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028417172, "dur": 1512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028418684, "dur": 56142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028474827, "dur": 6502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1756458028481371, "dur": 5493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1756458028486865, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756458028486933, "dur": 3252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1756458028490238, "dur": 8722504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028377973, "dur": 20984, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028398958, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_90248CE893D716A1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458028399375, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_68DE5687B3EC34A4.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458028399446, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028399602, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_37F3F60026223561.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458028399834, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1756458028399951, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1756458028400197, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756458028400428, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1756458028400551, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756458028400759, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1756458028400998, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756458028401134, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028401357, "dur": 2794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028404152, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028404368, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028404743, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028405233, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028406174, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028406546, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028406874, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028407195, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028407569, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028407790, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028408459, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028408822, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028409197, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028409506, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028409911, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028410625, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028411189, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028411679, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458028412394, "dur": 1007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756458028413402, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028413579, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028413735, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458028413955, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756458028414594, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028414694, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028415243, "dur": 555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028415798, "dur": 918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028416717, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756458028416809, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756458028417267, "dur": 1431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028418698, "dur": 56143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028474843, "dur": 5608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1756458028480452, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028480520, "dur": 3567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1756458028484124, "dur": 5085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1756458028489209, "dur": 405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028489632, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756458028490234, "dur": 8722529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028378011, "dur": 20908, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028398920, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_113F580BF08E1069.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458028399056, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_FA41A1679EA786AD.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458028399117, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028399240, "dur": 249, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C3D287ED16DDCE34.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458028399491, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_35E87E2AC2B68ABC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458028399870, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1756458028399943, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756458028400031, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756458028400248, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1756458028400437, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1756458028400514, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1756458028400629, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756458028400899, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756458028401119, "dur": 2982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028404102, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028404285, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028404517, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028404729, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028405888, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028406465, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028406893, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028407421, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028407986, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028408285, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028408726, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028409017, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028409419, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028409838, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028410211, "dur": 962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028411173, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028411603, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458028412176, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028412286, "dur": 1290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756458028413633, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028413741, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028414435, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756458028414820, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756458028415326, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028415815, "dur": 967, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028416782, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028417168, "dur": 1509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028418678, "dur": 56146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028474825, "dur": 6098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1756458028480924, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028481059, "dur": 6010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1756458028487070, "dur": 853, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028487935, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028488264, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028488444, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028488572, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028488846, "dur": 1014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756458028489860, "dur": 8722848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028378042, "dur": 20995, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028399038, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_76E612DE556D963F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458028399230, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_E9E3146384A07F6C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458028399467, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_0CB44D3D3D7D3B27.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458028399602, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_0CB44D3D3D7D3B27.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458028399821, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458028400024, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1756458028400252, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1756458028400489, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1756458028400651, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756458028400816, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756458028401011, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756458028401121, "dur": 2511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028403632, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028403765, "dur": 111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028403876, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028404009, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028404131, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028404334, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028404526, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028404732, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028405270, "dur": 536, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Nodes\\Input\\Scene\\ObjectNode.cs"}}, {"pid": 12345, "tid": 14, "ts": 1756458028405167, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028406272, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028406512, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028407061, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028407403, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028407602, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028407971, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028408354, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028408724, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028409018, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028409231, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028410038, "dur": 129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028410167, "dur": 1032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028411199, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028411588, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458028411750, "dur": 937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756458028412688, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028412887, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458028413261, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756458028413880, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756458028414166, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756458028414662, "dur": 579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028415289, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028415810, "dur": 978, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028416788, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028417173, "dur": 1518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028418691, "dur": 56121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028474839, "dur": 4106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1756458028479002, "dur": 3410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1756458028482448, "dur": 3784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1756458028486233, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756458028486363, "dur": 3616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1756458028490033, "dur": 8722715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028378066, "dur": 20888, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028398955, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_FE5DA9A8F69DF848.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458028399043, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_E30A78F0F4CABA72.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458028399151, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_79BD845FDB16458C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458028399219, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_79BD845FDB16458C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458028399445, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028399531, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_225D87A8B9941FD2.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458028399662, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_29DAA4DCAC1D5694.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458028399811, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458028399918, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756458028400050, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1756458028400157, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1756458028400249, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1756458028400586, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756458028400762, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1756458028400815, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8883882501886164173.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756458028401098, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028401431, "dur": 2804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028404236, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028404436, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028404675, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028405211, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028406185, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028406560, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028406899, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028407349, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028407611, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028407883, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028408269, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028408822, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028409097, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028409454, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028409949, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028410154, "dur": 1031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028411186, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028411607, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458028411994, "dur": 1189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756458028413183, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028413468, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458028413666, "dur": 1382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756458028415112, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028415246, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028415796, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756458028415943, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756458028416335, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028416774, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028417165, "dur": 1508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028418673, "dur": 56148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028474823, "dur": 4761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1756458028479584, "dur": 1100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028480688, "dur": 4205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1756458028484894, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028485350, "dur": 3746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1756458028489097, "dur": 719, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756458028489875, "dur": 8722806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028378097, "dur": 20848, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028398945, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1560055E669A2E68.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458028399067, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_E9AE314D2820AA3D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458028399179, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_E9AE314D2820AA3D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458028399441, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028399588, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_6AB2B904AF5C82BF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458028399947, "dur": 208, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756458028400181, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756458028400420, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1756458028400604, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756458028400737, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756458028400801, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9275553971400419791.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756458028400980, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2897256077774953845.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756458028401050, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15855758940564181747.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756458028401116, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028401321, "dur": 2841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028404162, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028404324, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028404612, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028405081, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028405907, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028406448, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028406683, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028407036, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028407531, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028407850, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028408140, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028408736, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028409371, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028409928, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028410821, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028411164, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028411583, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458028411937, "dur": 1384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756458028413380, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028413435, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028413545, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028413700, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458028414016, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756458028414661, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028415249, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028415795, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458028415937, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756458028416220, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028416345, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028416741, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028417161, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458028417290, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756458028417728, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458028417841, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756458028418116, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756458028418199, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756458028418392, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028418675, "dur": 56131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028474808, "dur": 2469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Unified.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1756458028477315, "dur": 4747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1756458028482063, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756458028482137, "dur": 4696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mobile.AndroidLogcat.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1756458028486877, "dur": 3181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1756458028490106, "dur": 8722596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028378144, "dur": 20737, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028398882, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_ECC6BD7BBFF90F85.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756458028399215, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_ECC6BD7BBFF90F85.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756458028399446, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028399601, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3A216315A8144C9D.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756458028399835, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1756458028400193, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1756458028400383, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1756458028400490, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1756458028400724, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1756458028400791, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1756458028401017, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1756458028401135, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028401389, "dur": 3110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028404500, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028404728, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028405138, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028406038, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028406258, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028406635, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028406913, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028407112, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028407428, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028407722, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028407949, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028408248, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028408560, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028408856, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028409210, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028410179, "dur": 1006, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028411185, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028411708, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756458028411892, "dur": 1016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1756458028412909, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028413004, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028413436, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028413554, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028413735, "dur": 703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028414438, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028414529, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028415245, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028415806, "dur": 990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028416796, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028417176, "dur": 1517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028418693, "dur": 56138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028474834, "dur": 4456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1756458028479291, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028479408, "dur": 5338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1756458028484747, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756458028484854, "dur": 2220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1756458028487111, "dur": 2954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1756458028490109, "dur": 8722595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028378170, "dur": 20706, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028398878, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_76639DDD4EDD4D5F.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458028399211, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_F68287F406F56905.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458028399303, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028399436, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_233E025D43731532.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458028399501, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_AF6907722A59D8EC.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458028399956, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756458028400260, "dur": 210, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756458028400517, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1756458028400588, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756458028400757, "dur": 283, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1756458028401041, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756458028401146, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028401333, "dur": 2457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028403791, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028403973, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028404130, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028404329, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028404609, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028405287, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Nodes\\Procedural\\Noise\\VoronoiNode.cs"}}, {"pid": 12345, "tid": 18, "ts": 1756458028405030, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028406058, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028406332, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028406598, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028406913, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028407322, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028407842, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028408121, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028408458, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028408851, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028409115, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028409409, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028409635, "dur": 57, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028409692, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028409938, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028410727, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028411196, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028411729, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458028412417, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1756458028413212, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028413462, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028413568, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028413743, "dur": 720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028414500, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458028414911, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028415008, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1756458028415376, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028415812, "dur": 1005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028416817, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028417166, "dur": 1504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028418671, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756458028418777, "dur": 56088, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028474866, "dur": 5752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1756458028480619, "dur": 957, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028481590, "dur": 6585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1756458028488176, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028488470, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028488657, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028488844, "dur": 1000, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756458028489845, "dur": 8722901, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028378206, "dur": 20663, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028398870, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_C393B5AAA8F80126.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458028399218, "dur": 208, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_C393B5AAA8F80126.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458028399428, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_3D3DFD266B8F6400.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458028399583, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_3D3DFD266B8F6400.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458028399911, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756458028400225, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756458028400403, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756458028401088, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028401625, "dur": 3018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028404643, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028405133, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028406060, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028406366, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028406635, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028406924, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028409198, "dur": 226, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.15f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 19, "ts": 1756458028409424, "dur": 776, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.15f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 19, "ts": 1756458028410200, "dur": 67, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.15f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 19, "ts": 1756458028407270, "dur": 3000, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028410270, "dur": 918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028411188, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028411607, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458028412107, "dur": 1982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1756458028414133, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458028414345, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1756458028415122, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028415266, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028415818, "dur": 948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028416766, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028417170, "dur": 1498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028418670, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756458028418811, "dur": 56023, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028474841, "dur": 6709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1756458028481551, "dur": 919, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028482479, "dur": 6616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1756458028489144, "dur": 955, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756458028490118, "dur": 8722622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028378241, "dur": 20621, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028398863, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_D26119ABA3CBFB9B.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458028399200, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_4936E8BF32CAB915.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458028399491, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_BCAB1FFD58D166C0.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458028399680, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028399736, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_7BAC05683089B125.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458028400142, "dur": 459, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1756458028400637, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756458028400789, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756458028400980, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13938709970307456411.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756458028401067, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756458028401156, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028401345, "dur": 3555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028404900, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028405834, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028406368, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028406610, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028406881, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028407313, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028407668, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028408078, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028408352, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028408702, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028409051, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028409290, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028409787, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028410208, "dur": 972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028411180, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028411580, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458028412121, "dur": 1475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1756458028413653, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756458028413796, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1756458028414454, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028414814, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028415254, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028415803, "dur": 916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028416719, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028417168, "dur": 1504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028418672, "dur": 56136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028474810, "dur": 2751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1756458028477562, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028477647, "dur": 4383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1756458028482031, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028482128, "dur": 6781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1756458028488972, "dur": 970, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756458028489962, "dur": 8722722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756458037221767, "dur": 1087, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 23380, "tid": 117, "ts": 1756458037224040, "dur": 14, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 23380, "tid": 117, "ts": 1756458037224082, "dur": 5699, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 23380, "tid": 117, "ts": 1756458037222232, "dur": 7587, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}