using System;
using System.Collections.Generic;
using UnityEngine;
using XLua;
using GameFramework.Core;

namespace GameFramework.XLua
{
    /// <summary>
    /// xLua配置类
    /// 定义需要导出到Lua的C#类型和接口
    /// </summary>
    public static class XLuaConfig
    {
        /// <summary>
        /// Lua调用C#配置
        /// </summary>
        [LuaCallCSharp]
        public static List<Type> LuaCallCSharp = new List<Type>()
        {
            // Unity基础类型
            typeof(UnityEngine.Object),
            typeof(GameObject),
            typeof(Component),
            typeof(Transform),
            typeof(MonoBehaviour),
            typeof(Vector2),
            typeof(Vector3),
            typeof(Quaternion),
            typeof(Time),
            typeof(Debug),
            typeof(Application),
            typeof(Screen),
            typeof(Input),
            typeof(PlayerPrefs),
            typeof(Mathf),
            typeof(UnityEngine.Random),
            typeof(Coroutine),
            typeof(WaitForSeconds),
            typeof(WaitForEndOfFrame),
            typeof(WaitForFixedUpdate),

            // UI相关
            typeof(Canvas),
            typeof(CanvasGroup),
            typeof(RectTransform),
            typeof(UnityEngine.UI.Button),
            typeof(UnityEngine.UI.Text),
            typeof(UnityEngine.UI.Image),
            typeof(UnityEngine.UI.Slider),
            typeof(UnityEngine.UI.Toggle),
            typeof(UnityEngine.UI.InputField),
            typeof(UnityEngine.UI.ScrollRect),

            // 游戏框架核心类型
            typeof(GameFramework.Core.GameFramework),
            typeof(IGameStateManager),
            typeof(ISceneManager),
            typeof(IUIManager),
            typeof(IResourceManager),
            typeof(IInputManager),

            // 枚举类型
            typeof(GameState),
            typeof(UIType),
            typeof(UILayer),
            typeof(ResourceType),
            typeof(LoadMode),
            typeof(SceneLoadMode),
            typeof(InputDeviceType),
            typeof(InputActionType),
            typeof(InputState),

            // xLua封装类型
            typeof(LuaGameFramework),
            typeof(LuaGameStateManager),
            typeof(LuaSceneManager),
            typeof(LuaUIManager),
            typeof(LuaResourceManager),
            typeof(LuaInputManager),
            typeof(LuaEventSystem),

            // 委托类型
            typeof(System.Action),
            typeof(System.Action<string>),
            typeof(System.Action<float>),
            typeof(System.Action<bool>),
            typeof(System.Action<GameState, GameState>),
            typeof(System.Action<InputActionType, InputState, object>),
            typeof(System.Action<InputDeviceType>),
            typeof(System.Func<bool>),
            typeof(UnityEngine.Events.UnityAction),
        };

        /// <summary>
        /// C#调用Lua配置
        /// </summary>
        [CSharpCallLua]
        public static List<Type> CSharpCallLua = new List<Type>()
        {
            // 委托类型
            typeof(System.Action),
            typeof(System.Action<string>),
            typeof(System.Action<float>),
            typeof(System.Action<bool>),
            typeof(System.Action<object>),
            typeof(System.Action<GameState, GameState>),
            typeof(System.Action<InputActionType, InputState, object>),
            typeof(System.Action<InputDeviceType>),
            typeof(System.Func<bool>),
            typeof(UnityEngine.Events.UnityAction),

            // Lua接口
            typeof(ILuaGameState),
            typeof(ILuaUIComponent),
            typeof(ILuaInputHandler),
        };

        /// <summary>
        /// 黑名单配置
        /// </summary>
        [BlackList]
        public static List<List<string>> BlackList = new List<List<string>>()
        {
            new List<string>(){"UnityEngine.WWW", "movie"},
            new List<string>(){"UnityEngine.Texture2D", "alphaIsTransparency"},
            new List<string>(){"UnityEngine.Security", "GetChainOfTrustValue"},
            new List<string>(){"UnityEngine.CanvasRenderer", "onRequestRebuild"},
            new List<string>(){"UnityEngine.Light", "areaSize"},
            new List<string>(){"UnityEngine.AnimatorOverrideController", "PerformOverrideClipListCleanup"},
            new List<string>(){"UnityEngine.AnimatorControllerParameter", "name"},
            new List<string>(){"UnityEngine.Caching", "SetNoBackupFlag"},
            new List<string>(){"UnityEngine.Caching", "ResetNoBackupFlag"},
        };

        /// <summary>
        /// GC优化配置
        /// </summary>
        [GCOptimize]
        public static List<Type> GCOptimize = new List<Type>()
        {
            typeof(UnityEngine.Vector2),
            typeof(UnityEngine.Vector3),
            typeof(UnityEngine.Vector4),
            typeof(UnityEngine.Color),
            typeof(UnityEngine.Quaternion),
            typeof(UnityEngine.Ray),
            typeof(UnityEngine.Bounds),
            typeof(UnityEngine.Ray2D),
        };
    }

    /// <summary>
    /// Lua游戏状态接口
    /// </summary>
    public interface ILuaGameState
    {
        void OnEnter();
        void OnUpdate();
        void OnExit();
        void OnDestroy();
    }

    /// <summary>
    /// Lua UI组件接口
    /// </summary>
    public interface ILuaUIComponent
    {
        void OnCreate();
        void OnShow();
        void OnHide();
        void OnDestroy();
        void OnUpdate();
    }

    /// <summary>
    /// Lua输入处理接口
    /// </summary>
    public interface ILuaInputHandler
    {
        void OnInputAction(InputActionType actionType, InputState state, object value);
        void OnDeviceChanged(InputDeviceType deviceType);
    }
}
