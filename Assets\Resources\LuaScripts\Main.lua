-- Main.lua
-- Unity6游戏框架 Lua主脚本示例

print("=== Unity6 游戏框架 Lua脚本开始执行 ===")

-- 全局变量
local gameData = {
    playerLevel = 1,
    playerScore = 0,
    gameTime = 0
}

-- 模块引用
local EventHelper = Event
local CoroutineHelper = Coroutine
local InputHelper = Input

-- 初始化函数
local function Initialize()
    print("Lua脚本初始化开始")
    
    -- 注册游戏生命周期事件
    RegisterGameEvents()
    
    -- 注册输入事件
    RegisterInputEvents()
    
    -- 初始化游戏数据
    InitializeGameData()
    
    -- 启动心跳定时器
    StartHeartbeat()
    
    print("Lua脚本初始化完成")
end

-- 注册游戏事件
function RegisterGameEvents()
    print("注册游戏事件")
    
    -- 游戏开始事件
    EventHelper.AddSimpleListener("GameStart", function()
        print("Lua: 游戏开始")
        gameData.gameTime = 0
        gameData.playerScore = 0
        
        -- 显示游戏UI
        GameFramework.UI.ShowUI("GameHUD")
        
        -- 播放开始音效
        PlaySound("GameStart")
    end)
    
    -- 游戏暂停事件
    EventHelper.AddSimpleListener("GamePause", function()
        print("Lua: 游戏暂停")
        -- 显示暂停菜单
        GameFramework.UI.CreateUI("PauseMenu", 2, 3) -- Popup, Popup layer
    end)
    
    -- 游戏结束事件
    EventHelper.AddSimpleListener("GameOver", function()
        print("Lua: 游戏结束")
        -- 保存游戏数据
        SaveGameData()
        
        -- 显示结算界面
        GameFramework.UI.CreateUI("GameOverUI", 1, 6) -- FullScreen, Top layer
    end)
    
    -- 玩家分数改变事件
    EventHelper.AddListener("PlayerScoreChange", function(data)
        if data then
            gameData.playerScore = data
            print("Lua: 玩家分数更新为 " .. gameData.playerScore)
            
            -- 检查是否升级
            CheckLevelUp()
        end
    end)
    
    -- 玩家升级事件
    EventHelper.AddListener("PlayerLevelUp", function(data)
        if data then
            gameData.playerLevel = data.level
            print("Lua: 玩家升级到等级 " .. gameData.playerLevel)
            
            -- 播放升级特效
            PlayLevelUpEffect()
        end
    end)
end

-- 注册输入事件
function RegisterInputEvents()
    print("注册输入事件")
    
    -- 注册Lua输入处理器
    GameFramework.Input.RegisterLuaInputHandler("MainInputHandler", {
        OnInputAction = function(actionType, state, value)
            HandleInputAction(actionType, state, value)
        end,
        
        OnDeviceChanged = function(deviceType)
            print("Lua: 输入设备切换到 " .. GameFramework.Input.GetDeviceTypeName(deviceType))
            -- 根据设备类型调整UI
            AdaptUIForDevice(deviceType)
        end
    })
end

-- 处理输入动作
function HandleInputAction(actionType, state, value)
    -- 移动输入
    if actionType == 1 and state == 2 then -- Move, Performed
        local moveVector = value
        if moveVector and (moveVector.x ~= 0 or moveVector.y ~= 0) then
            -- 处理移动逻辑
            EventHelper.TriggerEvent("PlayerMove", moveVector)
        end
    end
    
    -- 攻击输入
    if actionType == 3 and state == 1 then -- Attack, Started
        print("Lua: 检测到攻击输入")
        EventHelper.TriggerSimpleEvent("PlayerAttack")
    end
    
    -- 跳跃输入
    if actionType == 5 and state == 1 then -- Jump, Started
        print("Lua: 检测到跳跃输入")
        EventHelper.TriggerSimpleEvent("PlayerJump")
    end
    
    -- 暂停输入
    if actionType == 200 and state == 1 then -- Pause, Started
        print("Lua: 检测到暂停输入")
        TogglePause()
    end
end

-- 初始化游戏数据
function InitializeGameData()
    print("初始化游戏数据")
    
    -- 从本地存储加载数据
    LoadGameData()
    
    -- 设置默认值
    if gameData.playerLevel < 1 then
        gameData.playerLevel = 1
    end
    
    if gameData.playerScore < 0 then
        gameData.playerScore = 0
    end
    
    print("游戏数据初始化完成 - 等级: " .. gameData.playerLevel .. ", 分数: " .. gameData.playerScore)
end

-- 启动心跳定时器
function StartHeartbeat()
    print("启动心跳定时器")
    
    -- 每秒更新游戏时间
    CoroutineHelper.CreateTimer(1.0, function()
        gameData.gameTime = gameData.gameTime + 1
        
        -- 每10秒输出一次状态
        if gameData.gameTime % 10 == 0 then
            print("游戏运行时间: " .. gameData.gameTime .. "秒")
        end
        
        -- 触发时间更新事件
        EventHelper.TriggerEvent("GameTimeUpdate", gameData.gameTime)
    end, -1) -- 无限重复
end

-- 检查升级
function CheckLevelUp()
    local requiredScore = gameData.playerLevel * 1000
    if gameData.playerScore >= requiredScore then
        local newLevel = gameData.playerLevel + 1
        EventHelper.TriggerEvent("PlayerLevelUp", {
            level = newLevel,
            oldLevel = gameData.playerLevel,
            score = gameData.playerScore
        })
    end
end

-- 播放升级特效
function PlayLevelUpEffect()
    print("播放升级特效")
    
    -- 加载升级特效预制体
    GameFramework.Resource.LoadGameObjectAsync("Effects/LevelUp", function(effectPrefab)
        if effectPrefab then
            local effectInstance = GameObject.Instantiate(effectPrefab)
            print("升级特效实例化成功")
            
            -- 3秒后销毁特效
            CoroutineHelper.Delay(3.0, function()
                if effectInstance then
                    GameObject.Destroy(effectInstance)
                end
            end)
        end
    end)
    
    -- 播放升级音效
    PlaySound("LevelUp")
end

-- 播放音效
function PlaySound(soundName)
    local soundPath = "Audio/SFX/" .. soundName
    GameFramework.Resource.LoadAudioAsync(soundPath, function(audioClip)
        if audioClip then
            print("播放音效: " .. soundName)
            -- 这里需要音频播放逻辑
        else
            print("找不到音效: " .. soundName)
        end
    end)
end

-- 根据设备类型调整UI
function AdaptUIForDevice(deviceType)
    if deviceType == 1 then -- KeyboardMouse
        print("适配键盘鼠标UI")
        -- 显示鼠标光标
        -- 调整UI布局
    elseif deviceType == 2 then -- Gamepad
        print("适配手柄UI")
        -- 隐藏鼠标光标
        -- 显示手柄提示
    elseif deviceType == 3 then -- Touch
        print("适配触屏UI")
        -- 显示虚拟按钮
        -- 调整触控区域
    end
end

-- 切换暂停状态
function TogglePause()
    local currentState = GameFramework.GameState.CurrentState
    if currentState == 4 then -- InGame
        GameFramework.GameState.ChangeState(5) -- Paused
    elseif currentState == 5 then -- Paused
        GameFramework.GameState.ReturnToPreviousState()
    end
end

-- 保存游戏数据
function SaveGameData()
    print("保存游戏数据")
    -- 这里应该实现实际的数据保存逻辑
    -- 可以使用PlayerPrefs或文件系统
end

-- 加载游戏数据
function LoadGameData()
    print("加载游戏数据")
    -- 这里应该实现实际的数据加载逻辑
    -- 可以从PlayerPrefs或文件系统加载
end

-- 创建测试UI
function CreateTestUI()
    print("创建测试UI")
    
    -- 创建带Lua组件的UI
    local testUI = GameFramework.UI.CreateUIWithLua("TestUI", {
        OnCreate = function()
            print("测试UI创建")
        end,
        
        OnShow = function()
            print("测试UI显示")
        end,
        
        OnHide = function()
            print("测试UI隐藏")
        end,
        
        OnUpdate = function()
            -- UI更新逻辑
        end,
        
        OnDestroy = function()
            print("测试UI销毁")
        end
    }, 2, 3) -- Popup, Popup layer
    
    if testUI then
        print("测试UI创建成功")
        
        -- 3秒后自动关闭
        CoroutineHelper.Delay(3.0, function()
            GameFramework.UI.DestroyUI("TestUI")
        end)
    end
end

-- Unity生命周期函数
function Start()
    print("Lua Start函数调用")
    Initialize()
    
    -- 延迟创建测试UI
    CoroutineHelper.Delay(2.0, function()
        CreateTestUI()
    end)
end

function Update()
    -- 每帧更新逻辑
    -- 注意：避免在这里进行复杂计算
    
    -- 检查快捷键
    if InputHelper.GetCancelInput() then
        print("检测到取消输入")
    end
end

function FixedUpdate()
    -- 固定时间步长更新
    -- 适合物理相关的逻辑
end

function LateUpdate()
    -- 延迟更新
    -- 适合相机跟随等逻辑
end

function OnDestroy()
    print("Lua OnDestroy函数调用")
    
    -- 清理资源
    SaveGameData()
    
    -- 注销事件监听器
    EventHelper.ClearAllListeners()
    
    -- 注销输入处理器
    GameFramework.Input.UnregisterLuaInputHandler("MainInputHandler")
    
    print("Lua脚本清理完成")
end

-- 导出全局函数供C#调用
_G.GetGameData = function()
    return gameData
end

_G.SetPlayerScore = function(score)
    EventHelper.TriggerEvent("PlayerScoreChange", score)
end

_G.TriggerGameStart = function()
    EventHelper.TriggerSimpleEvent("GameStart")
end

_G.TriggerGameOver = function()
    EventHelper.TriggerSimpleEvent("GameOver")
end

print("=== Lua主脚本加载完成 ===")
