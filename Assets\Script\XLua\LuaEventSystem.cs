using System;
using System.Collections.Generic;
using UnityEngine;
using XLua;

namespace GameFramework.XLua
{
    /// <summary>
    /// Lua事件系统封装
    /// 提供Lua脚本之间的事件通信机制
    /// </summary>
    public class LuaEventSystem
    {
        private static LuaEventSystem instance;
        private Dictionary<string, List<System.Action<object>>> eventListeners = new Dictionary<string, List<System.Action<object>>>();
        private Dictionary<string, List<System.Action>> simpleEventListeners = new Dictionary<string, List<System.Action>>();

        public static LuaEventSystem Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new LuaEventSystem();
                }
                return instance;
            }
        }

        /// <summary>
        /// 注册事件监听器（带参数）
        /// </summary>
        /// <param name="eventName">事件名称</param>
        /// <param name="listener">监听器</param>
        public void AddListener(string eventName, System.Action<object> listener)
        {
            if (string.IsNullOrEmpty(eventName) || listener == null)
                return;

            if (!eventListeners.ContainsKey(eventName))
            {
                eventListeners[eventName] = new List<System.Action<object>>();
            }

            if (!eventListeners[eventName].Contains(listener))
            {
                eventListeners[eventName].Add(listener);
                Debug.Log($"[LuaEventSystem] 注册事件监听器: {eventName}");
            }
        }

        /// <summary>
        /// 注册事件监听器（无参数）
        /// </summary>
        /// <param name="eventName">事件名称</param>
        /// <param name="listener">监听器</param>
        public void AddSimpleListener(string eventName, System.Action listener)
        {
            if (string.IsNullOrEmpty(eventName) || listener == null)
                return;

            if (!simpleEventListeners.ContainsKey(eventName))
            {
                simpleEventListeners[eventName] = new List<System.Action>();
            }

            if (!simpleEventListeners[eventName].Contains(listener))
            {
                simpleEventListeners[eventName].Add(listener);
                Debug.Log($"[LuaEventSystem] 注册简单事件监听器: {eventName}");
            }
        }

        /// <summary>
        /// 移除事件监听器（带参数）
        /// </summary>
        /// <param name="eventName">事件名称</param>
        /// <param name="listener">监听器</param>
        public void RemoveListener(string eventName, System.Action<object> listener)
        {
            if (string.IsNullOrEmpty(eventName) || listener == null)
                return;

            if (eventListeners.ContainsKey(eventName))
            {
                eventListeners[eventName].Remove(listener);
                if (eventListeners[eventName].Count == 0)
                {
                    eventListeners.Remove(eventName);
                }
                Debug.Log($"[LuaEventSystem] 移除事件监听器: {eventName}");
            }
        }

        /// <summary>
        /// 移除事件监听器（无参数）
        /// </summary>
        /// <param name="eventName">事件名称</param>
        /// <param name="listener">监听器</param>
        public void RemoveSimpleListener(string eventName, System.Action listener)
        {
            if (string.IsNullOrEmpty(eventName) || listener == null)
                return;

            if (simpleEventListeners.ContainsKey(eventName))
            {
                simpleEventListeners[eventName].Remove(listener);
                if (simpleEventListeners[eventName].Count == 0)
                {
                    simpleEventListeners.Remove(eventName);
                }
                Debug.Log($"[LuaEventSystem] 移除简单事件监听器: {eventName}");
            }
        }

        /// <summary>
        /// 触发事件（带参数）
        /// </summary>
        /// <param name="eventName">事件名称</param>
        /// <param name="data">事件数据</param>
        public void TriggerEvent(string eventName, object data = null)
        {
            if (string.IsNullOrEmpty(eventName))
                return;

            Debug.Log($"[LuaEventSystem] 触发事件: {eventName}");

            if (eventListeners.ContainsKey(eventName))
            {
                var listeners = new List<System.Action<object>>(eventListeners[eventName]);
                foreach (var listener in listeners)
                {
                    try
                    {
                        listener?.Invoke(data);
                    }
                    catch (Exception e)
                    {
                        Debug.LogError($"[LuaEventSystem] 事件监听器异常: {eventName}, 错误: {e.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// 触发事件（无参数）
        /// </summary>
        /// <param name="eventName">事件名称</param>
        public void TriggerSimpleEvent(string eventName)
        {
            if (string.IsNullOrEmpty(eventName))
                return;

            Debug.Log($"[LuaEventSystem] 触发简单事件: {eventName}");

            if (simpleEventListeners.ContainsKey(eventName))
            {
                var listeners = new List<System.Action>(simpleEventListeners[eventName]);
                foreach (var listener in listeners)
                {
                    try
                    {
                        listener?.Invoke();
                    }
                    catch (Exception e)
                    {
                        Debug.LogError($"[LuaEventSystem] 简单事件监听器异常: {eventName}, 错误: {e.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// 移除指定事件的所有监听器
        /// </summary>
        /// <param name="eventName">事件名称</param>
        public void RemoveAllListeners(string eventName)
        {
            if (string.IsNullOrEmpty(eventName))
                return;

            if (eventListeners.ContainsKey(eventName))
            {
                eventListeners.Remove(eventName);
            }

            if (simpleEventListeners.ContainsKey(eventName))
            {
                simpleEventListeners.Remove(eventName);
            }

            Debug.Log($"[LuaEventSystem] 移除所有监听器: {eventName}");
        }

        /// <summary>
        /// 清除所有事件监听器
        /// </summary>
        public void ClearAllListeners()
        {
            eventListeners.Clear();
            simpleEventListeners.Clear();
            Debug.Log("[LuaEventSystem] 清除所有事件监听器");
        }

        /// <summary>
        /// 检查事件是否有监听器
        /// </summary>
        /// <param name="eventName">事件名称</param>
        /// <returns>是否有监听器</returns>
        public bool HasListeners(string eventName)
        {
            if (string.IsNullOrEmpty(eventName))
                return false;

            return (eventListeners.ContainsKey(eventName) && eventListeners[eventName].Count > 0) ||
                   (simpleEventListeners.ContainsKey(eventName) && simpleEventListeners[eventName].Count > 0);
        }

        /// <summary>
        /// 获取事件监听器数量
        /// </summary>
        /// <param name="eventName">事件名称</param>
        /// <returns>监听器数量</returns>
        public int GetListenerCount(string eventName)
        {
            if (string.IsNullOrEmpty(eventName))
                return 0;

            int count = 0;
            if (eventListeners.ContainsKey(eventName))
            {
                count += eventListeners[eventName].Count;
            }
            if (simpleEventListeners.ContainsKey(eventName))
            {
                count += simpleEventListeners[eventName].Count;
            }
            return count;
        }

        /// <summary>
        /// 获取所有事件名称
        /// </summary>
        /// <returns>事件名称数组</returns>
        public string[] GetAllEventNames()
        {
            var allEvents = new HashSet<string>();
            foreach (var eventName in eventListeners.Keys)
            {
                allEvents.Add(eventName);
            }
            foreach (var eventName in simpleEventListeners.Keys)
            {
                allEvents.Add(eventName);
            }
            return new List<string>(allEvents).ToArray();
        }

        /// <summary>
        /// 延迟触发事件
        /// </summary>
        /// <param name="eventName">事件名称</param>
        /// <param name="delay">延迟时间（秒）</param>
        /// <param name="data">事件数据</param>
        public void TriggerEventDelayed(string eventName, float delay, object data = null)
        {
            if (LuaCoroutineManager.Instance != null)
            {
                LuaCoroutineManager.Instance.StartDelayedAction(delay, () =>
                {
                    TriggerEvent(eventName, data);
                });
            }
        }

        /// <summary>
        /// 延迟触发简单事件
        /// </summary>
        /// <param name="eventName">事件名称</param>
        /// <param name="delay">延迟时间（秒）</param>
        public void TriggerSimpleEventDelayed(string eventName, float delay)
        {
            if (LuaCoroutineManager.Instance != null)
            {
                LuaCoroutineManager.Instance.StartDelayedAction(delay, () =>
                {
                    TriggerSimpleEvent(eventName);
                });
            }
        }
    }

    /// <summary>
    /// Lua事件系统辅助类
    /// 提供一些常用的事件名称常量和便捷方法
    /// </summary>
    public static class LuaEventHelper
    {
        // 游戏事件
        public const string GAME_START = "GameStart";
        public const string GAME_PAUSE = "GamePause";
        public const string GAME_RESUME = "GameResume";
        public const string GAME_OVER = "GameOver";
        public const string GAME_WIN = "GameWin";
        public const string GAME_LOSE = "GameLose";

        // 场景事件
        public const string SCENE_LOAD_START = "SceneLoadStart";
        public const string SCENE_LOAD_COMPLETE = "SceneLoadComplete";
        public const string SCENE_UNLOAD = "SceneUnload";

        // UI事件
        public const string UI_SHOW = "UIShow";
        public const string UI_HIDE = "UIHide";
        public const string UI_CLICK = "UIClick";
        public const string UI_CLOSE = "UIClose";

        // 玩家事件
        public const string PLAYER_SPAWN = "PlayerSpawn";
        public const string PLAYER_DIE = "PlayerDie";
        public const string PLAYER_LEVEL_UP = "PlayerLevelUp";
        public const string PLAYER_SCORE_CHANGE = "PlayerScoreChange";

        // 系统事件
        public const string SYSTEM_INIT = "SystemInit";
        public const string SYSTEM_SHUTDOWN = "SystemShutdown";
        public const string DEVICE_CHANGE = "DeviceChange";

        /// <summary>
        /// 触发游戏开始事件
        /// </summary>
        public static void TriggerGameStart()
        {
            LuaGameFramework.Event.TriggerSimpleEvent(GAME_START);
        }

        /// <summary>
        /// 触发游戏暂停事件
        /// </summary>
        public static void TriggerGamePause()
        {
            LuaGameFramework.Event.TriggerSimpleEvent(GAME_PAUSE);
        }

        /// <summary>
        /// 触发游戏恢复事件
        /// </summary>
        public static void TriggerGameResume()
        {
            LuaGameFramework.Event.TriggerSimpleEvent(GAME_RESUME);
        }

        /// <summary>
        /// 触发游戏结束事件
        /// </summary>
        /// <param name="isWin">是否胜利</param>
        public static void TriggerGameOver(bool isWin)
        {
            LuaGameFramework.Event.TriggerSimpleEvent(GAME_OVER);
            if (isWin)
            {
                LuaGameFramework.Event.TriggerSimpleEvent(GAME_WIN);
            }
            else
            {
                LuaGameFramework.Event.TriggerSimpleEvent(GAME_LOSE);
            }
        }

        /// <summary>
        /// 触发玩家分数改变事件
        /// </summary>
        /// <param name="newScore">新分数</param>
        public static void TriggerPlayerScoreChange(int newScore)
        {
            LuaGameFramework.Event.TriggerEvent(PLAYER_SCORE_CHANGE, newScore);
        }

        /// <summary>
        /// 触发UI点击事件
        /// </summary>
        /// <param name="uiName">UI名称</param>
        public static void TriggerUIClick(string uiName)
        {
            LuaGameFramework.Event.TriggerEvent(UI_CLICK, uiName);
        }

        /// <summary>
        /// 注册游戏生命周期事件
        /// </summary>
        /// <param name="onGameStart">游戏开始回调</param>
        /// <param name="onGamePause">游戏暂停回调</param>
        /// <param name="onGameResume">游戏恢复回调</param>
        /// <param name="onGameOver">游戏结束回调</param>
        public static void RegisterGameLifecycleEvents(
            System.Action onGameStart = null,
            System.Action onGamePause = null,
            System.Action onGameResume = null,
            System.Action onGameOver = null)
        {
            if (onGameStart != null)
                LuaGameFramework.Event.AddSimpleListener(GAME_START, onGameStart);
            if (onGamePause != null)
                LuaGameFramework.Event.AddSimpleListener(GAME_PAUSE, onGamePause);
            if (onGameResume != null)
                LuaGameFramework.Event.AddSimpleListener(GAME_RESUME, onGameResume);
            if (onGameOver != null)
                LuaGameFramework.Event.AddSimpleListener(GAME_OVER, onGameOver);
        }

        /// <summary>
        /// 注销游戏生命周期事件
        /// </summary>
        /// <param name="onGameStart">游戏开始回调</param>
        /// <param name="onGamePause">游戏暂停回调</param>
        /// <param name="onGameResume">游戏恢复回调</param>
        /// <param name="onGameOver">游戏结束回调</param>
        public static void UnregisterGameLifecycleEvents(
            System.Action onGameStart = null,
            System.Action onGamePause = null,
            System.Action onGameResume = null,
            System.Action onGameOver = null)
        {
            if (onGameStart != null)
                LuaGameFramework.Event.RemoveSimpleListener(GAME_START, onGameStart);
            if (onGamePause != null)
                LuaGameFramework.Event.RemoveSimpleListener(GAME_PAUSE, onGamePause);
            if (onGameResume != null)
                LuaGameFramework.Event.RemoveSimpleListener(GAME_RESUME, onGameResume);
            if (onGameOver != null)
                LuaGameFramework.Event.RemoveSimpleListener(GAME_OVER, onGameOver);
        }
    }
}
