# Unity6 游戏框架

这是一个为Unity6项目设计的完整游戏框架，集成了xLua支持，提供了游戏开发中常用的核心功能模块。

## 框架特性

- **模块化设计**: 各个管理器独立工作，可以单独使用或组合使用
- **事件驱动**: 基于事件的松耦合架构
- **易于扩展**: 提供清晰的接口，方便添加新功能
- **Unity6兼容**: 专为Unity6优化设计
- **xLua集成**: 支持Lua脚本开发

## 核心模块

### 1. 输入管理器 (InputManager)
封装Unity InputSystem，提供统一的输入管理接口。

**主要功能:**
- 多设备输入支持 (键盘鼠标、手柄、触屏等)
- 输入动作映射和事件系统
- 设备自动检测和切换
- 输入状态管理
- 动作映射的启用/禁用

**使用示例:**
```csharp
// 获取按钮输入
bool isJumping = GameFramework.Input.GetButton(InputActionType.Jump);
bool jumpPressed = GameFramework.Input.GetButtonDown(InputActionType.Jump);

// 获取轴输入
Vector2 moveInput = GameFramework.Input.GetVector2(InputActionType.Move);
Vector2 lookInput = GameFramework.Input.GetVector2(InputActionType.Look);

// 监听输入事件
GameFramework.Input.OnInputAction += (actionType, state, value) => {
    Debug.Log($"输入: {actionType} - {state} - {value}");
};

// 切换输入映射
GameFramework.Input.SwitchActionMap("UI");
```

### 2. 游戏状态管理器 (GameStateManager)
控制游戏从启动到关闭的整个生命周期流程。

**主要功能:**
- 游戏状态切换 (初始化、主菜单、游戏中、暂停、结束等)
- 状态事件监听
- 自定义状态处理器

**使用示例:**
```csharp
// 切换游戏状态
GameFramework.GameState.ChangeState(GameState.InGame);

// 监听状态改变
GameFramework.GameState.OnStateChanged += (oldState, newState) => {
    Debug.Log($"状态改变: {oldState} -> {newState}");
};
```

### 2. 场景管理器 (SceneManager)
负责场景的加载、卸载和切换管理。

**主要功能:**
- 同步/异步场景加载
- 场景加载进度监控
- 多场景管理
- 场景切换事件

**使用示例:**
```csharp
// 异步加载场景
GameFramework.Scene.LoadSceneAsync("GameScene");

// 监听加载进度
GameFramework.Scene.OnSceneLoadProgress += (progress) => {
    Debug.Log($"加载进度: {progress:P}");
};
```

### 3. 输入事件系统 (InputEventSystem)
提供更高级的输入事件处理和绑定功能。

**主要功能:**
- 输入事件优先级管理
- 输入事件组管理
- 手势识别支持
- 组合键支持
- 事件绑定和解绑

**使用示例:**
```csharp
// 创建输入事件系统
var inputEventSystem = gameObject.AddComponent<InputEventSystem>();

// 绑定输入事件
inputEventSystem.BindInputEvent(InputActionType.Attack, OnAttackInput, priority: 0);

// 创建事件组
inputEventSystem.CreateEventGroup("Player", true);
inputEventSystem.AddHandlerToGroup("Player", InputActionType.Move, OnMoveInput);

// 启用/禁用事件组
inputEventSystem.EnableEventGroup("Player");
inputEventSystem.DisableEventGroup("UI");
```

### 4. UI管理器 (UIManager)
管理全屏UI、弹出式UI的创建、显示、隐藏和销毁。

**主要功能:**
- UI层级管理
- UI生命周期管理
- 多种UI类型支持 (全屏、弹出、固定等)
- UI历史记录

**使用示例:**
```csharp
// 创建UI
var mainMenu = GameFramework.UI.CreateUI<MainMenuUI>("MainMenu", UIType.FullScreen);

// 显示/隐藏UI
GameFramework.UI.ShowUI("MainMenu");
GameFramework.UI.HideUI("MainMenu");
```

### 5. 资源管理器 (ResourceManager)
处理本地资源加载和远程资源下载、解压缩功能。

**主要功能:**
- 本地资源加载 (Resources、StreamingAssets)
- 远程资源下载
- 资源缓存管理
- 多种资源类型支持

**使用示例:**
```csharp
// 同步加载资源
var prefab = GameFramework.Resource.LoadResource<GameObject>("Prefabs/Player");

// 异步加载资源
GameFramework.Resource.LoadResourceAsync<Texture2D>("Textures/Icon", (texture) => {
    // 使用加载的纹理
});

// 下载远程资源
GameFramework.Resource.DownloadResource(url, savePath, onProgress, onComplete);
```

## 快速开始

### 1. 设置框架
1. 在场景中创建一个空的GameObject
2. 添加 `GameFramework` 组件
3. 配置需要的管理器选项

### 2. 使用框架
```csharp
using GameFramework.Core;

public class MyGameController : MonoBehaviour
{
    private void Start()
    {
        // 等待框架初始化
        if (GameFramework.Instance.IsInitialized)
        {
            OnFrameworkReady();
        }
        else
        {
            GameFramework.OnFrameworkInitialized += OnFrameworkReady;
        }
    }

    private void OnFrameworkReady()
    {
        // 框架已准备就绪，可以使用各个管理器
        GameFramework.GameState.ChangeState(GameState.MainMenu);
    }
}
```

### 3. 自定义状态处理器
```csharp
public class CustomGameState : IGameState
{
    public void Initialize() { }
    public void OnEnter() { }
    public void OnUpdate() { }
    public void OnExit() { }
    public void OnDestroy() { }
}

// 注册自定义状态
GameFramework.GameState.RegisterStateHandler(GameState.Custom, new CustomGameState());
```

## 目录结构

```
Assets/Script/Core/
├── GameEnums.cs              # 枚举定义
├── IManager.cs               # 管理器接口
├── GameStateManager.cs       # 游戏状态管理器
├── GameStates.cs            # 默认状态处理器
├── SceneManager.cs          # 场景管理器
├── UIManager.cs             # UI管理器
├── ResourceManager.cs       # 资源管理器
├── GameFramework.cs         # 框架主入口
├── Examples/
│   └── ExampleGameController.cs  # 使用示例
└── README.md                # 文档说明
```

## 配置说明

### GameFramework 组件配置
- **Enable Debug Log**: 是否启用调试日志
- **Auto Initialize**: 是否自动初始化
- **Dont Destroy On Load**: 是否在场景切换时保持
- **Enable XXX Manager**: 启用/禁用特定管理器

### 资源路径配置
- **UI资源路径**: `Resources/UI/`
- **本地资源路径**: `Resources/`
- **下载路径**: `Application.persistentDataPath/Downloads/`

## 扩展指南

### 添加新的管理器
1. 实现 `IManager` 接口
2. 在 `GameFramework` 中注册管理器
3. 提供静态访问属性

### 添加新的游戏状态
1. 在 `GameState` 枚举中添加新状态
2. 实现 `IGameState` 接口
3. 注册状态处理器

### 添加新的UI类型
1. 在 `UIType` 枚举中添加新类型
2. 在 `UIManager` 中处理新类型的逻辑

## 注意事项

1. 确保框架在使用前已经初始化完成
2. 资源路径需要正确配置
3. UI预制体需要放在正确的Resources目录下
4. 远程下载需要网络权限
5. 建议在真机上测试资源加载功能

## 示例场景

运行示例控制器 `ExampleGameController` 可以体验框架的基本功能:
- 按键1-3: 切换游戏状态
- 按键L: 加载场景示例
- 按键U: UI操作示例
- 按键R: 资源加载示例

## 技术支持

如有问题或建议，请查看代码注释或联系开发团队。
