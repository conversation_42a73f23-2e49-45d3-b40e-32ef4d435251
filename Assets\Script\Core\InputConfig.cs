using System;
using System.Collections.Generic;
using UnityEngine;

namespace GameFramework.Core
{
    /// <summary>
    /// 输入配置
    /// 管理输入设置和键位绑定
    /// </summary>
    [CreateAssetMenu(fileName = "InputConfig", menuName = "GameFramework/Input Config")]
    public class InputConfig : ScriptableObject
    {
        [Header("输入设置")]
        [SerializeField] private float mouseSensitivity = 2f;
        [SerializeField] private float gamepadSensitivity = 100f;
        [SerializeField] private float touchSensitivity = 1f;
        [SerializeField] private bool invertMouseY = false;
        [SerializeField] private bool invertGamepadY = false;

        [Header("死区设置")]
        [SerializeField] private float gamepadDeadzone = 0.1f;
        [SerializeField] private float triggerDeadzone = 0.1f;

        [Header("振动设置")]
        [SerializeField] private bool enableVibration = true;
        [SerializeField] private float vibrationIntensity = 1f;

        [Header("输入映射设置")]
        [SerializeField] private List<InputActionMapping> actionMappings = new List<InputActionMapping>();

        public float MouseSensitivity => mouseSensitivity;
        public float GamepadSensitivity => gamepadSensitivity;
        public float TouchSensitivity => touchSensitivity;
        public bool InvertMouseY => invertMouseY;
        public bool InvertGamepadY => invertGamepadY;
        public float GamepadDeadzone => gamepadDeadzone;
        public float TriggerDeadzone => triggerDeadzone;
        public bool EnableVibration => enableVibration;
        public float VibrationIntensity => vibrationIntensity;
        public List<InputActionMapping> ActionMappings => actionMappings;

        /// <summary>
        /// 获取设备特定的灵敏度
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>灵敏度值</returns>
        public float GetSensitivity(InputDeviceType deviceType)
        {
            return deviceType switch
            {
                InputDeviceType.KeyboardMouse => mouseSensitivity,
                InputDeviceType.Gamepad => gamepadSensitivity,
                InputDeviceType.Touch => touchSensitivity,
                _ => 1f
            };
        }

        /// <summary>
        /// 设置设备特定的灵敏度
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <param name="sensitivity">灵敏度值</param>
        public void SetSensitivity(InputDeviceType deviceType, float sensitivity)
        {
            switch (deviceType)
            {
                case InputDeviceType.KeyboardMouse:
                    mouseSensitivity = sensitivity;
                    break;
                case InputDeviceType.Gamepad:
                    gamepadSensitivity = sensitivity;
                    break;
                case InputDeviceType.Touch:
                    touchSensitivity = sensitivity;
                    break;
            }
        }

        /// <summary>
        /// 获取Y轴反转设置
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>是否反转</returns>
        public bool GetInvertY(InputDeviceType deviceType)
        {
            return deviceType switch
            {
                InputDeviceType.KeyboardMouse => invertMouseY,
                InputDeviceType.Gamepad => invertGamepadY,
                _ => false
            };
        }

        /// <summary>
        /// 设置Y轴反转
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <param name="invert">是否反转</param>
        public void SetInvertY(InputDeviceType deviceType, bool invert)
        {
            switch (deviceType)
            {
                case InputDeviceType.KeyboardMouse:
                    invertMouseY = invert;
                    break;
                case InputDeviceType.Gamepad:
                    invertGamepadY = invert;
                    break;
            }
        }

        /// <summary>
        /// 获取动作映射
        /// </summary>
        /// <param name="actionType">动作类型</param>
        /// <returns>映射信息</returns>
        public InputActionMapping GetActionMapping(InputActionType actionType)
        {
            return actionMappings.Find(mapping => mapping.actionType == actionType);
        }

        /// <summary>
        /// 添加或更新动作映射
        /// </summary>
        /// <param name="mapping">映射信息</param>
        public void SetActionMapping(InputActionMapping mapping)
        {
            int index = actionMappings.FindIndex(m => m.actionType == mapping.actionType);
            if (index >= 0)
            {
                actionMappings[index] = mapping;
            }
            else
            {
                actionMappings.Add(mapping);
            }
        }

        /// <summary>
        /// 重置为默认设置
        /// </summary>
        public void ResetToDefaults()
        {
            mouseSensitivity = 2f;
            gamepadSensitivity = 100f;
            touchSensitivity = 1f;
            invertMouseY = false;
            invertGamepadY = false;
            gamepadDeadzone = 0.1f;
            triggerDeadzone = 0.1f;
            enableVibration = true;
            vibrationIntensity = 1f;
            actionMappings.Clear();
        }

        /// <summary>
        /// 保存配置到PlayerPrefs
        /// </summary>
        public void SaveToPlayerPrefs()
        {
            PlayerPrefs.SetFloat("Input_MouseSensitivity", mouseSensitivity);
            PlayerPrefs.SetFloat("Input_GamepadSensitivity", gamepadSensitivity);
            PlayerPrefs.SetFloat("Input_TouchSensitivity", touchSensitivity);
            PlayerPrefs.SetInt("Input_InvertMouseY", invertMouseY ? 1 : 0);
            PlayerPrefs.SetInt("Input_InvertGamepadY", invertGamepadY ? 1 : 0);
            PlayerPrefs.SetFloat("Input_GamepadDeadzone", gamepadDeadzone);
            PlayerPrefs.SetFloat("Input_TriggerDeadzone", triggerDeadzone);
            PlayerPrefs.SetInt("Input_EnableVibration", enableVibration ? 1 : 0);
            PlayerPrefs.SetFloat("Input_VibrationIntensity", vibrationIntensity);
            PlayerPrefs.Save();
        }

        /// <summary>
        /// 从PlayerPrefs加载配置
        /// </summary>
        public void LoadFromPlayerPrefs()
        {
            mouseSensitivity = PlayerPrefs.GetFloat("Input_MouseSensitivity", mouseSensitivity);
            gamepadSensitivity = PlayerPrefs.GetFloat("Input_GamepadSensitivity", gamepadSensitivity);
            touchSensitivity = PlayerPrefs.GetFloat("Input_TouchSensitivity", touchSensitivity);
            invertMouseY = PlayerPrefs.GetInt("Input_InvertMouseY", invertMouseY ? 1 : 0) == 1;
            invertGamepadY = PlayerPrefs.GetInt("Input_InvertGamepadY", invertGamepadY ? 1 : 0) == 1;
            gamepadDeadzone = PlayerPrefs.GetFloat("Input_GamepadDeadzone", gamepadDeadzone);
            triggerDeadzone = PlayerPrefs.GetFloat("Input_TriggerDeadzone", triggerDeadzone);
            enableVibration = PlayerPrefs.GetInt("Input_EnableVibration", enableVibration ? 1 : 0) == 1;
            vibrationIntensity = PlayerPrefs.GetFloat("Input_VibrationIntensity", vibrationIntensity);
        }
    }

    /// <summary>
    /// 输入动作映射
    /// </summary>
    [Serializable]
    public class InputActionMapping
    {
        [Header("动作信息")]
        public InputActionType actionType;
        public string displayName;
        public string description;

        [Header("键位绑定")]
        public List<string> keyboardBindings = new List<string>();
        public List<string> gamepadBindings = new List<string>();
        public List<string> touchBindings = new List<string>();

        [Header("设置")]
        public bool canRebind = true;
        public bool isEnabled = true;
        public float holdTime = 0f; // 长按时间要求
        public bool requiresDoublePress = false; // 是否需要双击

        public InputActionMapping()
        {
        }

        public InputActionMapping(InputActionType actionType, string displayName)
        {
            this.actionType = actionType;
            this.displayName = displayName;
        }

        /// <summary>
        /// 获取设备特定的绑定
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>绑定列表</returns>
        public List<string> GetBindings(InputDeviceType deviceType)
        {
            return deviceType switch
            {
                InputDeviceType.KeyboardMouse => keyboardBindings,
                InputDeviceType.Gamepad => gamepadBindings,
                InputDeviceType.Touch => touchBindings,
                _ => new List<string>()
            };
        }

        /// <summary>
        /// 设置设备特定的绑定
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <param name="bindings">绑定列表</param>
        public void SetBindings(InputDeviceType deviceType, List<string> bindings)
        {
            switch (deviceType)
            {
                case InputDeviceType.KeyboardMouse:
                    keyboardBindings = new List<string>(bindings);
                    break;
                case InputDeviceType.Gamepad:
                    gamepadBindings = new List<string>(bindings);
                    break;
                case InputDeviceType.Touch:
                    touchBindings = new List<string>(bindings);
                    break;
            }
        }

        /// <summary>
        /// 添加绑定
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <param name="binding">绑定字符串</param>
        public void AddBinding(InputDeviceType deviceType, string binding)
        {
            var bindings = GetBindings(deviceType);
            if (!bindings.Contains(binding))
            {
                bindings.Add(binding);
            }
        }

        /// <summary>
        /// 移除绑定
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <param name="binding">绑定字符串</param>
        public void RemoveBinding(InputDeviceType deviceType, string binding)
        {
            var bindings = GetBindings(deviceType);
            bindings.Remove(binding);
        }
    }

    /// <summary>
    /// 输入配置管理器
    /// </summary>
    public static class InputConfigManager
    {
        private static InputConfig currentConfig;

        /// <summary>
        /// 当前配置
        /// </summary>
        public static InputConfig CurrentConfig
        {
            get
            {
                if (currentConfig == null)
                {
                    LoadConfig();
                }
                return currentConfig;
            }
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        public static void LoadConfig()
        {
            currentConfig = Resources.Load<InputConfig>("InputConfig");
            if (currentConfig == null)
            {
                currentConfig = ScriptableObject.CreateInstance<InputConfig>();
                Debug.LogWarning("[InputConfigManager] 找不到InputConfig资源，使用默认配置");
            }
            else
            {
                currentConfig.LoadFromPlayerPrefs();
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public static void SaveConfig()
        {
            if (currentConfig != null)
            {
                currentConfig.SaveToPlayerPrefs();
            }
        }

        /// <summary>
        /// 重置配置
        /// </summary>
        public static void ResetConfig()
        {
            if (currentConfig != null)
            {
                currentConfig.ResetToDefaults();
                SaveConfig();
            }
        }

        /// <summary>
        /// 应用配置到输入管理器
        /// </summary>
        public static void ApplyConfig()
        {
            if (GameFramework.Input != null && currentConfig != null)
            {
                // 这里可以添加将配置应用到输入管理器的逻辑
                Debug.Log("[InputConfigManager] 配置已应用到输入管理器");
            }
        }
    }
}
