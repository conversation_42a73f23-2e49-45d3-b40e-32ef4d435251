using UnityEngine;

namespace GameFramework.Core
{
    /// <summary>
    /// 初始化状态
    /// </summary>
    public class InitializeState : IGameState
    {
        public void Initialize()
        {
            Debug.Log("[InitializeState] 状态处理器初始化");
        }

        public void OnEnter()
        {
            Debug.Log("[InitializeState] 进入初始化状态");
            // 在这里执行游戏初始化逻辑
            // 例如：加载配置文件、初始化各种管理器等
            
            // 初始化完成后切换到主菜单
            var gameStateManager = Object.FindObjectOfType<GameStateManager>();
            if (gameStateManager != null)
            {
                gameStateManager.ChangeState(GameState.MainMenu);
            }
        }

        public void OnUpdate()
        {
            // 初始化状态的更新逻辑
        }

        public void OnExit()
        {
            Debug.Log("[InitializeState] 退出初始化状态");
        }

        public void OnDestroy()
        {
            Debug.Log("[InitializeState] 销毁状态处理器");
        }
    }

    /// <summary>
    /// 加载状态
    /// </summary>
    public class LoadingState : IGameState
    {
        public void Initialize()
        {
            Debug.Log("[LoadingState] 状态处理器初始化");
        }

        public void OnEnter()
        {
            Debug.Log("[LoadingState] 进入加载状态");
            // 显示加载界面
            // 开始加载资源
        }

        public void OnUpdate()
        {
            // 更新加载进度
        }

        public void OnExit()
        {
            Debug.Log("[LoadingState] 退出加载状态");
            // 隐藏加载界面
        }

        public void OnDestroy()
        {
            Debug.Log("[LoadingState] 销毁状态处理器");
        }
    }

    /// <summary>
    /// 主菜单状态
    /// </summary>
    public class MainMenuState : IGameState
    {
        public void Initialize()
        {
            Debug.Log("[MainMenuState] 状态处理器初始化");
        }

        public void OnEnter()
        {
            Debug.Log("[MainMenuState] 进入主菜单状态");
            // 显示主菜单UI
            // 播放背景音乐
        }

        public void OnUpdate()
        {
            // 主菜单状态的更新逻辑
        }

        public void OnExit()
        {
            Debug.Log("[MainMenuState] 退出主菜单状态");
            // 隐藏主菜单UI
        }

        public void OnDestroy()
        {
            Debug.Log("[MainMenuState] 销毁状态处理器");
        }
    }

    /// <summary>
    /// 游戏中状态
    /// </summary>
    public class InGameState : IGameState
    {
        public void Initialize()
        {
            Debug.Log("[InGameState] 状态处理器初始化");
        }

        public void OnEnter()
        {
            Debug.Log("[InGameState] 进入游戏状态");
            // 显示游戏UI
            // 开始游戏逻辑
        }

        public void OnUpdate()
        {
            // 游戏状态的更新逻辑
        }

        public void OnExit()
        {
            Debug.Log("[InGameState] 退出游戏状态");
            // 暂停游戏逻辑
        }

        public void OnDestroy()
        {
            Debug.Log("[InGameState] 销毁状态处理器");
        }
    }

    /// <summary>
    /// 暂停状态
    /// </summary>
    public class PausedState : IGameState
    {
        public void Initialize()
        {
            Debug.Log("[PausedState] 状态处理器初始化");
        }

        public void OnEnter()
        {
            Debug.Log("[PausedState] 进入暂停状态");
            // 显示暂停菜单
            // 暂停游戏时间
            Time.timeScale = 0f;
        }

        public void OnUpdate()
        {
            // 暂停状态的更新逻辑
        }

        public void OnExit()
        {
            Debug.Log("[PausedState] 退出暂停状态");
            // 隐藏暂停菜单
            // 恢复游戏时间
            Time.timeScale = 1f;
        }

        public void OnDestroy()
        {
            Debug.Log("[PausedState] 销毁状态处理器");
        }
    }

    /// <summary>
    /// 游戏结束状态
    /// </summary>
    public class GameOverState : IGameState
    {
        public void Initialize()
        {
            Debug.Log("[GameOverState] 状态处理器初始化");
        }

        public void OnEnter()
        {
            Debug.Log("[GameOverState] 进入游戏结束状态");
            // 显示游戏结束界面
            // 保存游戏数据
        }

        public void OnUpdate()
        {
            // 游戏结束状态的更新逻辑
        }

        public void OnExit()
        {
            Debug.Log("[GameOverState] 退出游戏结束状态");
            // 隐藏游戏结束界面
        }

        public void OnDestroy()
        {
            Debug.Log("[GameOverState] 销毁状态处理器");
        }
    }

    /// <summary>
    /// 设置状态
    /// </summary>
    public class SettingsState : IGameState
    {
        public void Initialize()
        {
            Debug.Log("[SettingsState] 状态处理器初始化");
        }

        public void OnEnter()
        {
            Debug.Log("[SettingsState] 进入设置状态");
            // 显示设置界面
        }

        public void OnUpdate()
        {
            // 设置状态的更新逻辑
        }

        public void OnExit()
        {
            Debug.Log("[SettingsState] 退出设置状态");
            // 隐藏设置界面
            // 保存设置
        }

        public void OnDestroy()
        {
            Debug.Log("[SettingsState] 销毁状态处理器");
        }
    }

    /// <summary>
    /// 退出状态
    /// </summary>
    public class QuitState : IGameState
    {
        public void Initialize()
        {
            Debug.Log("[QuitState] 状态处理器初始化");
        }

        public void OnEnter()
        {
            Debug.Log("[QuitState] 进入退出状态");
            // 保存游戏数据
            // 清理资源
            // 退出应用程序
            Application.Quit();
        }

        public void OnUpdate()
        {
            // 退出状态的更新逻辑
        }

        public void OnExit()
        {
            Debug.Log("[QuitState] 退出退出状态");
        }

        public void OnDestroy()
        {
            Debug.Log("[QuitState] 销毁状态处理器");
        }
    }
}
