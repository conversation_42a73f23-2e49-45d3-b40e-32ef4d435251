using System;
using System.Collections.Generic;
using UnityEngine;
using XLua;
using GameFramework.Core;

namespace GameFramework.XLua
{
    /// <summary>
    /// Lua游戏状态管理器封装
    /// </summary>
    public class LuaGameStateManager
    {
        private static LuaGameStateManager instance;
        private Dictionary<GameState, ILuaGameState> luaStateHandlers = new Dictionary<GameState, ILuaGameState>();

        public static LuaGameStateManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new LuaGameStateManager();
                }
                return instance;
            }
        }

        /// <summary>
        /// 当前游戏状态
        /// </summary>
        public GameState CurrentState
        {
            get
            {
                return GameFramework.Core.GameFramework.GameState?.CurrentState ?? GameState.None;
            }
        }

        /// <summary>
        /// 上一个游戏状态
        /// </summary>
        public GameState PreviousState
        {
            get
            {
                return GameFramework.Core.GameFramework.GameState?.PreviousState ?? GameState.None;
            }
        }

        /// <summary>
        /// 切换游戏状态
        /// </summary>
        /// <param name="newState">新状态</param>
        public void ChangeState(GameState newState)
        {
            GameFramework.Core.GameFramework.GameState?.ChangeState(newState);
        }

        /// <summary>
        /// 返回上一个状态
        /// </summary>
        public void ReturnToPreviousState()
        {
            GameFramework.Core.GameFramework.GameState?.ReturnToPreviousState();
        }

        /// <summary>
        /// 注册Lua状态处理器
        /// </summary>
        /// <param name="state">游戏状态</param>
        /// <param name="handler">Lua状态处理器</param>
        public void RegisterLuaStateHandler(GameState state, ILuaGameState handler)
        {
            if (luaStateHandlers.ContainsKey(state))
            {
                Debug.LogWarning($"[LuaGameStateManager] 替换Lua状态处理器: {state}");
            }

            luaStateHandlers[state] = handler;

            // 创建C#状态处理器包装
            var csharpHandler = new LuaGameStateWrapper(handler);
            GameFramework.Core.GameFramework.GameState?.RegisterStateHandler(state, csharpHandler);

            Debug.Log($"[LuaGameStateManager] 注册Lua状态处理器: {state}");
        }

        /// <summary>
        /// 注销Lua状态处理器
        /// </summary>
        /// <param name="state">游戏状态</param>
        public void UnregisterLuaStateHandler(GameState state)
        {
            if (luaStateHandlers.ContainsKey(state))
            {
                luaStateHandlers.Remove(state);
                GameFramework.Core.GameFramework.GameState?.UnregisterStateHandler(state);
                Debug.Log($"[LuaGameStateManager] 注销Lua状态处理器: {state}");
            }
        }

        /// <summary>
        /// 注册状态改变事件
        /// </summary>
        /// <param name="callback">回调函数</param>
        public void OnStateChanged(System.Action<GameState, GameState> callback)
        {
            if (GameFramework.Core.GameFramework.GameState != null)
            {
                GameFramework.Core.GameFramework.GameState.OnStateChanged += callback;
            }
        }

        /// <summary>
        /// 注销状态改变事件
        /// </summary>
        /// <param name="callback">回调函数</param>
        public void OffStateChanged(System.Action<GameState, GameState> callback)
        {
            if (GameFramework.Core.GameFramework.GameState != null)
            {
                GameFramework.Core.GameFramework.GameState.OnStateChanged -= callback;
            }
        }

        /// <summary>
        /// 检查是否为指定状态
        /// </summary>
        /// <param name="state">要检查的状态</param>
        /// <returns>是否为指定状态</returns>
        public bool IsState(GameState state)
        {
            return CurrentState == state;
        }

        /// <summary>
        /// 检查是否可以切换到指定状态
        /// </summary>
        /// <param name="state">目标状态</param>
        /// <returns>是否可以切换</returns>
        public bool CanChangeToState(GameState state)
        {
            // 这里可以添加状态切换的逻辑检查
            return CurrentState != state;
        }

        /// <summary>
        /// 获取状态名称
        /// </summary>
        /// <param name="state">游戏状态</param>
        /// <returns>状态名称</returns>
        public string GetStateName(GameState state)
        {
            return state.ToString();
        }

        /// <summary>
        /// 获取当前状态名称
        /// </summary>
        /// <returns>当前状态名称</returns>
        public string GetCurrentStateName()
        {
            return GetStateName(CurrentState);
        }

        /// <summary>
        /// 获取上一个状态名称
        /// </summary>
        /// <returns>上一个状态名称</returns>
        public string GetPreviousStateName()
        {
            return GetStateName(PreviousState);
        }
    }

    /// <summary>
    /// Lua游戏状态包装器
    /// 将Lua状态处理器包装为C#状态处理器
    /// </summary>
    public class LuaGameStateWrapper : IGameState
    {
        private ILuaGameState luaHandler;

        public LuaGameStateWrapper(ILuaGameState luaHandler)
        {
            this.luaHandler = luaHandler;
        }

        public void Initialize()
        {
            // Lua状态处理器不需要Initialize
        }

        public void OnEnter()
        {
            try
            {
                luaHandler?.OnEnter();
            }
            catch (Exception e)
            {
                Debug.LogError($"[LuaGameStateWrapper] OnEnter异常: {e.Message}");
            }
        }

        public void OnUpdate()
        {
            try
            {
                luaHandler?.OnUpdate();
            }
            catch (Exception e)
            {
                Debug.LogError($"[LuaGameStateWrapper] OnUpdate异常: {e.Message}");
            }
        }

        public void OnExit()
        {
            try
            {
                luaHandler?.OnExit();
            }
            catch (Exception e)
            {
                Debug.LogError($"[LuaGameStateWrapper] OnExit异常: {e.Message}");
            }
        }

        public void OnDestroy()
        {
            try
            {
                luaHandler?.OnDestroy();
            }
            catch (Exception e)
            {
                Debug.LogError($"[LuaGameStateWrapper] OnDestroy异常: {e.Message}");
            }
        }
    }

    /// <summary>
    /// Lua游戏状态辅助类
    /// 提供一些便捷的状态操作方法
    /// </summary>
    public static class LuaGameStateHelper
    {
        /// <summary>
        /// 切换到初始化状态
        /// </summary>
        public static void ToInitialize()
        {
            LuaGameFramework.GameState.ChangeState(GameState.Initialize);
        }

        /// <summary>
        /// 切换到加载状态
        /// </summary>
        public static void ToLoading()
        {
            LuaGameFramework.GameState.ChangeState(GameState.Loading);
        }

        /// <summary>
        /// 切换到主菜单状态
        /// </summary>
        public static void ToMainMenu()
        {
            LuaGameFramework.GameState.ChangeState(GameState.MainMenu);
        }

        /// <summary>
        /// 切换到游戏状态
        /// </summary>
        public static void ToInGame()
        {
            LuaGameFramework.GameState.ChangeState(GameState.InGame);
        }

        /// <summary>
        /// 切换到暂停状态
        /// </summary>
        public static void ToPaused()
        {
            LuaGameFramework.GameState.ChangeState(GameState.Paused);
        }

        /// <summary>
        /// 切换到游戏结束状态
        /// </summary>
        public static void ToGameOver()
        {
            LuaGameFramework.GameState.ChangeState(GameState.GameOver);
        }

        /// <summary>
        /// 切换到设置状态
        /// </summary>
        public static void ToSettings()
        {
            LuaGameFramework.GameState.ChangeState(GameState.Settings);
        }

        /// <summary>
        /// 切换到退出状态
        /// </summary>
        public static void ToQuit()
        {
            LuaGameFramework.GameState.ChangeState(GameState.Quit);
        }

        /// <summary>
        /// 检查是否在游戏中
        /// </summary>
        /// <returns>是否在游戏中</returns>
        public static bool IsInGame()
        {
            return LuaGameFramework.GameState.IsState(GameState.InGame);
        }

        /// <summary>
        /// 检查是否在主菜单
        /// </summary>
        /// <returns>是否在主菜单</returns>
        public static bool IsInMainMenu()
        {
            return LuaGameFramework.GameState.IsState(GameState.MainMenu);
        }

        /// <summary>
        /// 检查是否暂停
        /// </summary>
        /// <returns>是否暂停</returns>
        public static bool IsPaused()
        {
            return LuaGameFramework.GameState.IsState(GameState.Paused);
        }

        /// <summary>
        /// 检查是否在加载
        /// </summary>
        /// <returns>是否在加载</returns>
        public static bool IsLoading()
        {
            return LuaGameFramework.GameState.IsState(GameState.Loading);
        }
    }
}
