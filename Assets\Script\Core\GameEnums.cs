using System;

namespace GameFramework.Core
{
    /// <summary>
    /// 游戏状态枚举
    /// </summary>
    public enum GameState
    {
        None = 0,
        Initialize = 1,     // 初始化
        Loading = 2,        // 加载中
        MainMenu = 3,       // 主菜单
        InGame = 4,         // 游戏中
        Paused = 5,         // 暂停
        GameOver = 6,       // 游戏结束
        Settings = 7,       // 设置界面
        Quit = 8           // 退出游戏
    }

    /// <summary>
    /// UI类型枚举
    /// </summary>
    public enum UIType
    {
        None = 0,
        FullScreen = 1,     // 全屏UI
        Popup = 2,          // 弹出式UI
        Fixed = 3,          // 固定UI（如HUD）
        Loading = 4,        // 加载UI
        Dialog = 5          // 对话框UI
    }

    /// <summary>
    /// UI层级枚举
    /// </summary>
    public enum UILayer
    {
        Background = 0,     // 背景层
        Normal = 1,         // 普通层
        Fixed = 2,          // 固定层
        Popup = 3,          // 弹出层
        Guide = 4,          // 引导层
        Notice = 5,         // 通知层
        Top = 6            // 顶层
    }

    /// <summary>
    /// 资源类型枚举
    /// </summary>
    public enum ResourceType
    {
        None = 0,
        Prefab = 1,         // 预制体
        Scene = 2,          // 场景
        Texture = 3,        // 纹理
        Audio = 4,          // 音频
        Material = 5,       // 材质
        Animation = 6,      // 动画
        Font = 7,           // 字体
        Shader = 8,         // 着色器
        Config = 9,         // 配置文件
        Lua = 10           // Lua脚本
    }

    /// <summary>
    /// 资源加载方式枚举
    /// </summary>
    public enum LoadMode
    {
        Local = 0,          // 本地加载
        Remote = 1,         // 远程下载
        StreamingAssets = 2 // StreamingAssets加载
    }

    /// <summary>
    /// 场景加载模式枚举
    /// </summary>
    public enum SceneLoadMode
    {
        Single = 0,         // 单场景模式
        Additive = 1        // 叠加模式
    }

    /// <summary>
    /// 音频类型枚举
    /// </summary>
    public enum AudioType
    {
        None = 0,
        BGM = 1,            // 背景音乐
        SFX = 2,            // 音效
        Voice = 3           // 语音
    }
}
