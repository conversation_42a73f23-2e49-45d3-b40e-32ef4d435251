using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.InputSystem;

namespace GameFramework.Core
{
    /// <summary>
    /// 输入管理器
    /// 封装Unity InputSystem，提供统一的输入管理接口
    /// </summary>
    public class InputManager : MonoBehaviour, IInputManager
    {
        [Header("输入管理器设置")]
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private bool enableInputOnStart = true;
        [SerializeField] private InputActionAsset inputActionAsset;

        private InputDeviceType currentDeviceType = InputDeviceType.KeyboardMouse;
        private bool inputEnabled = true;
        private Dictionary<InputActionType, InputAction> actionMap = new Dictionary<InputActionType, InputAction>();
        private Dictionary<InputActionType, bool> buttonStates = new Dictionary<InputActionType, bool>();
        private Dictionary<InputActionType, bool> buttonDownStates = new Dictionary<InputActionType, bool>();
        private Dictionary<InputActionType, bool> buttonUpStates = new Dictionary<InputActionType, bool>();

        // 输入动作映射
        private InputActionMap playerActionMap;
        private InputActionMap uiActionMap;

        public string Name => "InputManager";
        public bool IsInitialized { get; private set; }
        public InputDeviceType CurrentDeviceType => currentDeviceType;
        public bool InputEnabled 
        { 
            get => inputEnabled; 
            set 
            { 
                inputEnabled = value;
                SetInputEnabled(value);
            } 
        }

        public event Action<InputActionType, InputState, object> OnInputAction;
        public event Action<InputDeviceType> OnDeviceChanged;

        private void Awake()
        {
            // 确保单例
            if (FindObjectsOfType<InputManager>().Length > 1)
            {
                Destroy(gameObject);
                return;
            }
            DontDestroyOnLoad(gameObject);
        }

        private void Start()
        {
            Initialize();
        }

        private void Update()
        {
            if (IsInitialized && inputEnabled)
            {
                UpdateButtonStates();
                DetectDeviceChange();
            }
        }

        public void Initialize()
        {
            if (IsInitialized)
                return;

            LogDebug("初始化输入管理器");

            try
            {
                // 加载输入动作资源
                if (inputActionAsset == null)
                {
                    inputActionAsset = Resources.Load<InputActionAsset>("InputSystem_Actions");
                }

                if (inputActionAsset == null)
                {
                    LogDebug("找不到InputSystem_Actions资源文件");
                    return;
                }

                // 获取动作映射
                playerActionMap = inputActionAsset.FindActionMap("Player");
                uiActionMap = inputActionAsset.FindActionMap("UI");

                // 初始化动作映射
                InitializeActionMaps();

                // 启用输入
                if (enableInputOnStart)
                {
                    EnableInput();
                }

                IsInitialized = true;
                LogDebug("输入管理器初始化完成");
            }
            catch (Exception e)
            {
                LogDebug($"输入管理器初始化失败: {e.Message}");
                Debug.LogException(e);
            }
        }



        public void Destroy()
        {
            LogDebug("销毁输入管理器");

            // 禁用所有输入
            DisableInput();

            // 清理事件
            OnInputAction = null;
            OnDeviceChanged = null;

            // 清理数据
            actionMap.Clear();
            buttonStates.Clear();
            buttonDownStates.Clear();
            buttonUpStates.Clear();

            IsInitialized = false;
        }

        public bool GetButton(InputActionType actionType)
        {
            return buttonStates.ContainsKey(actionType) && buttonStates[actionType];
        }

        public bool GetButtonDown(InputActionType actionType)
        {
            bool result = buttonDownStates.ContainsKey(actionType) && buttonDownStates[actionType];
            if (result)
            {
                buttonDownStates[actionType] = false; // 重置状态
            }
            return result;
        }

        public bool GetButtonUp(InputActionType actionType)
        {
            bool result = buttonUpStates.ContainsKey(actionType) && buttonUpStates[actionType];
            if (result)
            {
                buttonUpStates[actionType] = false; // 重置状态
            }
            return result;
        }

        public float GetAxis(InputActionType actionType)
        {
            if (actionMap.ContainsKey(actionType))
            {
                var action = actionMap[actionType];
                if (action.expectedControlType == "Vector2")
                {
                    Vector2 value = action.ReadValue<Vector2>();
                    return actionType == InputActionType.Move ? value.x : value.y;
                }
                return action.ReadValue<float>();
            }
            return 0f;
        }

        public Vector2 GetVector2(InputActionType actionType)
        {
            if (actionMap.ContainsKey(actionType))
            {
                return actionMap[actionType].ReadValue<Vector2>();
            }
            return Vector2.zero;
        }

        public void EnableActionMap(string mapName)
        {
            var actionMap = inputActionAsset.FindActionMap(mapName);
            if (actionMap != null)
            {
                actionMap.Enable();
                LogDebug($"启用输入映射: {mapName}");
            }
        }

        public void DisableActionMap(string mapName)
        {
            var actionMap = inputActionAsset.FindActionMap(mapName);
            if (actionMap != null)
            {
                actionMap.Disable();
                LogDebug($"禁用输入映射: {mapName}");
            }
        }

        public void SwitchActionMap(string mapName)
        {
            // 禁用所有映射
            inputActionAsset.Disable();
            
            // 启用指定映射
            EnableActionMap(mapName);
            LogDebug($"切换到输入映射: {mapName}");
        }

        /// <summary>
        /// 启用输入
        /// </summary>
        public void EnableInput()
        {
            if (inputActionAsset != null)
            {
                inputActionAsset.Enable();
                inputEnabled = true;
                LogDebug("启用输入系统");
            }
        }

        /// <summary>
        /// 禁用输入
        /// </summary>
        public void DisableInput()
        {
            if (inputActionAsset != null)
            {
                inputActionAsset.Disable();
                inputEnabled = false;
                LogDebug("禁用输入系统");
            }
        }

        /// <summary>
        /// 设置输入启用状态
        /// </summary>
        private void SetInputEnabled(bool enabled)
        {
            if (enabled)
            {
                EnableInput();
            }
            else
            {
                DisableInput();
            }
        }

        /// <summary>
        /// 初始化动作映射
        /// </summary>
        private void InitializeActionMaps()
        {
            // 玩家动作映射
            if (playerActionMap != null)
            {
                MapPlayerActions();
            }

            // UI动作映射
            if (uiActionMap != null)
            {
                MapUIActions();
            }

            LogDebug($"动作映射初始化完成，共 {actionMap.Count} 个动作");
        }

        /// <summary>
        /// 映射玩家动作
        /// </summary>
        private void MapPlayerActions()
        {
            MapAction(InputActionType.Move, playerActionMap.FindAction("Move"));
            MapAction(InputActionType.Look, playerActionMap.FindAction("Look"));
            MapAction(InputActionType.Attack, playerActionMap.FindAction("Attack"));
            MapAction(InputActionType.Interact, playerActionMap.FindAction("Interact"));
            MapAction(InputActionType.Jump, playerActionMap.FindAction("Jump"));
            MapAction(InputActionType.Crouch, playerActionMap.FindAction("Crouch"));
            MapAction(InputActionType.Sprint, playerActionMap.FindAction("Sprint"));
            MapAction(InputActionType.Previous, playerActionMap.FindAction("Previous"));
            MapAction(InputActionType.Next, playerActionMap.FindAction("Next"));
        }

        /// <summary>
        /// 映射UI动作
        /// </summary>
        private void MapUIActions()
        {
            MapAction(InputActionType.Navigate, uiActionMap.FindAction("Navigate"));
            MapAction(InputActionType.Submit, uiActionMap.FindAction("Submit"));
            MapAction(InputActionType.Cancel, uiActionMap.FindAction("Cancel"));
            MapAction(InputActionType.Point, uiActionMap.FindAction("Point"));
            MapAction(InputActionType.Click, uiActionMap.FindAction("Click"));
            MapAction(InputActionType.RightClick, uiActionMap.FindAction("RightClick"));
            MapAction(InputActionType.MiddleClick, uiActionMap.FindAction("MiddleClick"));
            MapAction(InputActionType.ScrollWheel, uiActionMap.FindAction("ScrollWheel"));
        }

        /// <summary>
        /// 映射单个动作
        /// </summary>
        private void MapAction(InputActionType actionType, InputAction action)
        {
            if (action != null)
            {
                actionMap[actionType] = action;
                
                // 绑定事件
                action.started += (context) => OnActionCallback(actionType, InputState.Started, context);
                action.performed += (context) => OnActionCallback(actionType, InputState.Performed, context);
                action.canceled += (context) => OnActionCallback(actionType, InputState.Canceled, context);

                LogDebug($"映射动作: {actionType} -> {action.name}");
            }
        }

        /// <summary>
        /// 动作回调
        /// </summary>
        private void OnActionCallback(InputActionType actionType, InputState state, InputAction.CallbackContext context)
        {
            // 更新按钮状态
            UpdateButtonState(actionType, state, context);

            // 触发事件
            object value = GetActionValue(context);
            OnInputAction?.Invoke(actionType, state, value);

            LogDebug($"输入动作: {actionType} - {state} - {value}");
        }

        /// <summary>
        /// 获取动作值
        /// </summary>
        private object GetActionValue(InputAction.CallbackContext context)
        {
            try
            {
                if (context.valueType == typeof(float))
                    return context.ReadValue<float>();
                else if (context.valueType == typeof(Vector2))
                    return context.ReadValue<Vector2>();
                else if (context.valueType == typeof(bool))
                    return context.ReadValue<bool>();
                else
                    return context.ReadValueAsObject();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 更新按钮状态
        /// </summary>
        private void UpdateButtonState(InputActionType actionType, InputState state, InputAction.CallbackContext context)
        {
            switch (state)
            {
                case InputState.Started:
                    buttonStates[actionType] = true;
                    buttonDownStates[actionType] = true;
                    buttonUpStates[actionType] = false;
                    break;
                case InputState.Canceled:
                    buttonStates[actionType] = false;
                    buttonDownStates[actionType] = false;
                    buttonUpStates[actionType] = true;
                    break;
            }
        }

        /// <summary>
        /// 更新按钮状态
        /// </summary>
        private void UpdateButtonStates()
        {
            // 这里可以添加额外的按钮状态更新逻辑
        }

        /// <summary>
        /// 检测设备变化
        /// </summary>
        private void DetectDeviceChange()
        {
            InputDeviceType newDeviceType = GetCurrentDeviceType();
            if (newDeviceType != currentDeviceType)
            {
                currentDeviceType = newDeviceType;
                OnDeviceChanged?.Invoke(currentDeviceType);
                LogDebug($"输入设备切换: {currentDeviceType}");
            }
        }

        /// <summary>
        /// 获取当前设备类型
        /// </summary>
        private InputDeviceType GetCurrentDeviceType()
        {
            // 检测最近使用的设备
            if (Gamepad.current != null && Gamepad.current.wasUpdatedThisFrame)
                return InputDeviceType.Gamepad;
            else if (Touchscreen.current != null && Touchscreen.current.wasUpdatedThisFrame)
                return InputDeviceType.Touch;
            else if (Keyboard.current != null && Keyboard.current.wasUpdatedThisFrame)
                return InputDeviceType.KeyboardMouse;
            else if (Mouse.current != null && Mouse.current.wasUpdatedThisFrame)
                return InputDeviceType.KeyboardMouse;

            return currentDeviceType; // 保持当前设备类型
        }

        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[InputManager] {message}");
            }
        }

        void IManager.Update()
        {
            // 接口实现，避免与MonoBehaviour的Update冲突
        }
    }
}
