using System;
using UnityEngine;
using XLua;
using GameFramework.Core;

namespace GameFramework.XLua
{
    /// <summary>
    /// Lua资源管理器封装
    /// </summary>
    public class LuaResourceManager
    {
        private static LuaResourceManager instance;

        public static LuaResourceManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new LuaResourceManager();
                }
                return instance;
            }
        }

        /// <summary>
        /// 加载GameObject资源
        /// </summary>
        /// <param name="path">资源路径</param>
        /// <param name="loadMode">加载模式 0=Local, 1=Remote, 2=StreamingAssets</param>
        /// <returns>GameObject</returns>
        public GameObject LoadGameObject(string path, int loadMode = 0)
        {
            LoadMode mode = (LoadMode)loadMode;
            return GameFramework.Core.GameFramework.Resource?.LoadResource<GameObject>(path, mode);
        }

        /// <summary>
        /// 异步加载GameObject资源
        /// </summary>
        /// <param name="path">资源路径</param>
        /// <param name="callback">加载完成回调</param>
        /// <param name="loadMode">加载模式</param>
        public void LoadGameObjectAsync(string path, System.Action<GameObject> callback, int loadMode = 0)
        {
            LoadMode mode = (LoadMode)loadMode;
            GameFramework.Core.GameFramework.Resource?.LoadResourceAsync<GameObject>(path, callback, mode);
        }

        /// <summary>
        /// 加载纹理资源
        /// </summary>
        /// <param name="path">资源路径</param>
        /// <param name="loadMode">加载模式</param>
        /// <returns>Texture2D</returns>
        public Texture2D LoadTexture(string path, int loadMode = 0)
        {
            LoadMode mode = (LoadMode)loadMode;
            return GameFramework.Core.GameFramework.Resource?.LoadResource<Texture2D>(path, mode);
        }

        /// <summary>
        /// 异步加载纹理资源
        /// </summary>
        /// <param name="path">资源路径</param>
        /// <param name="callback">加载完成回调</param>
        /// <param name="loadMode">加载模式</param>
        public void LoadTextureAsync(string path, System.Action<Texture2D> callback, int loadMode = 0)
        {
            LoadMode mode = (LoadMode)loadMode;
            GameFramework.Core.GameFramework.Resource?.LoadResourceAsync<Texture2D>(path, callback, mode);
        }

        /// <summary>
        /// 加载音频资源
        /// </summary>
        /// <param name="path">资源路径</param>
        /// <param name="loadMode">加载模式</param>
        /// <returns>AudioClip</returns>
        public AudioClip LoadAudio(string path, int loadMode = 0)
        {
            LoadMode mode = (LoadMode)loadMode;
            return GameFramework.Core.GameFramework.Resource?.LoadResource<AudioClip>(path, mode);
        }

        /// <summary>
        /// 异步加载音频资源
        /// </summary>
        /// <param name="path">资源路径</param>
        /// <param name="callback">加载完成回调</param>
        /// <param name="loadMode">加载模式</param>
        public void LoadAudioAsync(string path, System.Action<AudioClip> callback, int loadMode = 0)
        {
            LoadMode mode = (LoadMode)loadMode;
            GameFramework.Core.GameFramework.Resource?.LoadResourceAsync<AudioClip>(path, callback, mode);
        }

        /// <summary>
        /// 加载文本资源
        /// </summary>
        /// <param name="path">资源路径</param>
        /// <param name="loadMode">加载模式</param>
        /// <returns>TextAsset</returns>
        public TextAsset LoadText(string path, int loadMode = 0)
        {
            LoadMode mode = (LoadMode)loadMode;
            return GameFramework.Core.GameFramework.Resource?.LoadResource<TextAsset>(path, mode);
        }

        /// <summary>
        /// 异步加载文本资源
        /// </summary>
        /// <param name="path">资源路径</param>
        /// <param name="callback">加载完成回调</param>
        /// <param name="loadMode">加载模式</param>
        public void LoadTextAsync(string path, System.Action<TextAsset> callback, int loadMode = 0)
        {
            LoadMode mode = (LoadMode)loadMode;
            GameFramework.Core.GameFramework.Resource?.LoadResourceAsync<TextAsset>(path, callback, mode);
        }

        /// <summary>
        /// 卸载资源
        /// </summary>
        /// <param name="path">资源路径</param>
        public void UnloadResource(string path)
        {
            GameFramework.Core.GameFramework.Resource?.UnloadResource(path);
        }

        /// <summary>
        /// 下载远程资源
        /// </summary>
        /// <param name="url">下载地址</param>
        /// <param name="savePath">保存路径</param>
        /// <param name="onProgress">下载进度回调</param>
        /// <param name="onComplete">下载完成回调</param>
        public void DownloadResource(string url, string savePath, System.Action<float> onProgress = null, System.Action<bool> onComplete = null)
        {
            GameFramework.Core.GameFramework.Resource?.DownloadResource(url, savePath, onProgress, onComplete);
        }

        /// <summary>
        /// 实例化GameObject
        /// </summary>
        /// <param name="path">预制体路径</param>
        /// <param name="parent">父对象</param>
        /// <param name="loadMode">加载模式</param>
        /// <returns>实例化的GameObject</returns>
        public GameObject Instantiate(string path, Transform parent = null, int loadMode = 0)
        {
            GameObject prefab = LoadGameObject(path, loadMode);
            if (prefab != null)
            {
                return UnityEngine.Object.Instantiate(prefab, parent);
            }
            return null;
        }

        /// <summary>
        /// 异步实例化GameObject
        /// </summary>
        /// <param name="path">预制体路径</param>
        /// <param name="callback">实例化完成回调</param>
        /// <param name="parent">父对象</param>
        /// <param name="loadMode">加载模式</param>
        public void InstantiateAsync(string path, System.Action<GameObject> callback, Transform parent = null, int loadMode = 0)
        {
            LoadGameObjectAsync(path, (prefab) =>
            {
                if (prefab != null)
                {
                    GameObject instance = UnityEngine.Object.Instantiate(prefab, parent);
                    callback?.Invoke(instance);
                }
                else
                {
                    callback?.Invoke(null);
                }
            }, loadMode);
        }

        /// <summary>
        /// 获取资源信息
        /// </summary>
        /// <param name="path">资源路径</param>
        /// <returns>资源信息字符串</returns>
        public string GetResourceInfo(string path)
        {
            var resourceManager = GameFramework.Core.GameFramework.Resource as GameFramework.Core.ResourceManager;
            var resourceInfo = resourceManager?.GetResourceInfo(path);
            
            if (resourceInfo != null)
            {
                return $"路径:{resourceInfo.path}, 类型:{resourceInfo.resourceType}, 加载模式:{resourceInfo.loadMode}, 加载时间:{resourceInfo.loadTime}";
            }
            return "资源不存在";
        }

        /// <summary>
        /// 获取已加载资源列表
        /// </summary>
        /// <returns>资源路径数组</returns>
        public string[] GetLoadedResources()
        {
            var resourceManager = GameFramework.Core.GameFramework.Resource as GameFramework.Core.ResourceManager;
            var resourceList = resourceManager?.GetLoadedResourcePaths();
            return resourceList?.ToArray() ?? new string[0];
        }

        /// <summary>
        /// 检查资源是否已加载
        /// </summary>
        /// <param name="path">资源路径</param>
        /// <returns>是否已加载</returns>
        public bool IsResourceLoaded(string path)
        {
            var resourceManager = GameFramework.Core.GameFramework.Resource as GameFramework.Core.ResourceManager;
            return resourceManager?.GetResourceInfo(path) != null;
        }
    }

    /// <summary>
    /// Lua资源管理辅助类
    /// </summary>
    public static class LuaResourceHelper
    {
        /// <summary>
        /// 加载UI预制体
        /// </summary>
        /// <param name="uiName">UI名称</param>
        /// <param name="callback">加载完成回调</param>
        public static void LoadUIPrefab(string uiName, System.Action<GameObject> callback)
        {
            string path = $"UI/{uiName}";
            LuaGameFramework.Resource.LoadGameObjectAsync(path, callback);
        }

        /// <summary>
        /// 加载角色预制体
        /// </summary>
        /// <param name="characterName">角色名称</param>
        /// <param name="callback">加载完成回调</param>
        public static void LoadCharacterPrefab(string characterName, System.Action<GameObject> callback)
        {
            string path = $"Characters/{characterName}";
            LuaGameFramework.Resource.LoadGameObjectAsync(path, callback);
        }

        /// <summary>
        /// 加载特效预制体
        /// </summary>
        /// <param name="effectName">特效名称</param>
        /// <param name="callback">加载完成回调</param>
        public static void LoadEffectPrefab(string effectName, System.Action<GameObject> callback)
        {
            string path = $"Effects/{effectName}";
            LuaGameFramework.Resource.LoadGameObjectAsync(path, callback);
        }

        /// <summary>
        /// 加载配置文件
        /// </summary>
        /// <param name="configName">配置文件名</param>
        /// <param name="callback">加载完成回调</param>
        public static void LoadConfig(string configName, System.Action<string> callback)
        {
            string path = $"Configs/{configName}";
            LuaGameFramework.Resource.LoadTextAsync(path, (textAsset) =>
            {
                string content = textAsset?.text ?? "";
                callback?.Invoke(content);
            });
        }

        /// <summary>
        /// 加载本地化文本
        /// </summary>
        /// <param name="language">语言</param>
        /// <param name="fileName">文件名</param>
        /// <param name="callback">加载完成回调</param>
        public static void LoadLocalization(string language, string fileName, System.Action<string> callback)
        {
            string path = $"Localization/{language}/{fileName}";
            LuaGameFramework.Resource.LoadTextAsync(path, (textAsset) =>
            {
                string content = textAsset?.text ?? "";
                callback?.Invoke(content);
            });
        }

        /// <summary>
        /// 批量加载资源
        /// </summary>
        /// <param name="paths">资源路径数组</param>
        /// <param name="onProgress">进度回调</param>
        /// <param name="onComplete">完成回调</param>
        public static void LoadResourcesBatch(string[] paths, System.Action<float> onProgress = null, System.Action onComplete = null)
        {
            if (paths == null || paths.Length == 0)
            {
                onComplete?.Invoke();
                return;
            }

            int loadedCount = 0;
            int totalCount = paths.Length;

            foreach (string path in paths)
            {
                LuaGameFramework.Resource.LoadGameObjectAsync(path, (obj) =>
                {
                    loadedCount++;
                    float progress = (float)loadedCount / totalCount;
                    onProgress?.Invoke(progress);

                    if (loadedCount >= totalCount)
                    {
                        onComplete?.Invoke();
                    }
                });
            }
        }

        /// <summary>
        /// 预加载常用资源
        /// </summary>
        /// <param name="onComplete">完成回调</param>
        public static void PreloadCommonResources(System.Action onComplete = null)
        {
            string[] commonPaths = {
                "UI/Loading",
                "UI/MessageBox",
                "Effects/Common/Explosion",
                "Audio/BGM/MainTheme"
            };

            LoadResourcesBatch(commonPaths, null, onComplete);
        }

        /// <summary>
        /// 清理未使用的资源
        /// </summary>
        public static void CleanupUnusedResources()
        {
            Resources.UnloadUnusedAssets();
            System.GC.Collect();
            Debug.Log("[LuaResourceHelper] 清理未使用的资源完成");
        }

        /// <summary>
        /// 获取资源加载统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public static string GetResourceStats()
        {
            string[] loadedResources = LuaGameFramework.Resource.GetLoadedResources();
            return $"已加载资源数量: {loadedResources.Length}";
        }
    }
}
