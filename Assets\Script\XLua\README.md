# Unity6 游戏框架 xLua 集成

这是Unity6游戏框架的xLua集成模块，为Lua脚本提供了完整的游戏框架访问接口。

## 功能特性

- **完整的框架封装**: 将所有游戏框架功能暴露给Lua
- **类型安全**: 提供强类型的Lua接口定义
- **事件系统**: Lua脚本间的事件通信机制
- **协程支持**: 为Lua提供协程和定时器功能
- **热重载**: 支持Lua脚本的热重载和调试
- **性能优化**: 合理的GC配置和内存管理

## 核心组件

### 1. LuaManager (Lua管理器)
负责Lua环境的初始化、脚本加载和执行。

**主要功能:**
- Lua环境管理
- 脚本加载和执行
- C#与Lua的双向调用
- 热重载支持

### 2. LuaGameFramework (Lua游戏框架封装)
为Lua提供游戏框架的主要接口。

**可用模块:**
```lua
-- 游戏状态管理
GameFramework.GameState.ChangeState(1) -- 切换到主菜单

-- 场景管理
GameFramework.Scene.LoadSceneAsync("GameLevel")

-- UI管理
GameFramework.UI.CreateUI("MainMenu", 1, 1) -- 创建全屏UI

-- 资源管理
GameFramework.Resource.LoadGameObjectAsync("Prefabs/Player", callback)

-- 输入管理
local moveInput = GameFramework.Input.GetVector2(1) -- 获取移动输入

-- 事件系统
GameFramework.Event.TriggerEvent("PlayerDie", playerData)

-- 协程系统
Coroutine.Delay(2.0, function() print("延迟执行") end)
```

### 3. LuaEventSystem (Lua事件系统)
提供Lua脚本间的事件通信机制。

**使用示例:**
```lua
-- 注册事件监听器
Event.AddListener("PlayerLevelUp", function(data)
    print("玩家升级到等级: " .. data.level)
end)

-- 触发事件
Event.TriggerEvent("PlayerLevelUp", {level = 5, exp = 1000})

-- 延迟触发事件
Event.TriggerEventDelayed("GameStart", 3.0)
```

### 4. LuaCoroutineManager (Lua协程管理器)
为Lua提供协程和定时器功能。

**使用示例:**
```lua
-- 延迟执行
Coroutine.Delay(1.0, function()
    print("1秒后执行")
end)

-- 重复执行
local repeatId = Coroutine.Repeat(0.5, function()
    print("每0.5秒执行一次")
end, 10) -- 重复10次

-- 渐变动画
Coroutine.Tween(2.0, function(progress)
    -- progress从0到1
    transform.position = Vector3.Lerp(startPos, endPos, progress)
end, function()
    print("动画完成")
end)

-- 创建定时器
local timerId = Coroutine.CreateTimer(1.0, function()
    print("定时器触发")
end, -1) -- 无限重复
```

## 快速开始

### 1. 设置Lua管理器
在场景中创建一个GameObject并添加LuaManager组件：

```csharp
// 或者通过代码创建
GameObject luaManagerObject = new GameObject("LuaManager");
LuaManager luaManager = luaManagerObject.AddComponent<LuaManager>();
```

### 2. 创建Lua脚本
在`Resources/LuaScripts/`目录下创建`Main.lua`：

```lua
-- Main.lua
print("Lua脚本开始执行")

-- 全局生命周期函数
function Start()
    print("Lua Start")
    
    -- 注册游戏事件
    Event.AddSimpleListener("GameStart", function()
        print("游戏开始")
    end)
    
    -- 创建定时器
    Coroutine.CreateTimer(1.0, function()
        print("心跳: " .. Time.time)
    end)
end

function Update()
    -- 检查输入
    if Input.GetJumpInput() then
        print("检测到跳跃输入")
        Event.TriggerSimpleEvent("PlayerJump")
    end
end

function OnDestroy()
    print("Lua脚本销毁")
end
```

### 3. Lua UI组件示例
```lua
-- UI/MainMenuUI.lua
local MainMenuUI = {}

function MainMenuUI:OnCreate()
    print("主菜单UI创建")
    self.gameObject = GameFramework.UI.GetUI("MainMenu")
end

function MainMenuUI:OnShow()
    print("主菜单UI显示")
    -- 播放显示动画
    Coroutine.Tween(0.5, function(progress)
        -- 淡入效果
        self:SetAlpha(progress)
    end)
end

function MainMenuUI:OnHide()
    print("主菜单UI隐藏")
end

function MainMenuUI:OnUpdate()
    -- UI更新逻辑
end

function MainMenuUI:OnDestroy()
    print("主菜单UI销毁")
end

return MainMenuUI
```

### 4. Lua游戏状态示例
```lua
-- States/MainMenuState.lua
local MainMenuState = {}

function MainMenuState:OnEnter()
    print("进入主菜单状态")
    
    -- 显示主菜单UI
    GameFramework.UI.ShowUI("MainMenu")
    
    -- 播放背景音乐
    GameFramework.Resource.LoadAudioAsync("Audio/BGM/MainMenu", function(clip)
        if clip then
            -- 播放音乐
        end
    end)
end

function MainMenuState:OnUpdate()
    -- 检查输入
    if Input.GetSubmitInput() then
        -- 开始游戏
        GameFramework.GameState.ChangeState(4) -- InGame状态
    end
end

function MainMenuState:OnExit()
    print("退出主菜单状态")
    GameFramework.UI.HideUI("MainMenu")
end

function MainMenuState:OnDestroy()
    print("主菜单状态销毁")
end

return MainMenuState
```

## 配置说明

### xLua配置 (XLuaConfig.cs)
配置需要导出到Lua的C#类型：

```csharp
[LuaCallCSharp]
public static List<Type> LuaCallCSharp = new List<Type>()
{
    // 添加需要在Lua中使用的C#类型
    typeof(GameObject),
    typeof(Transform),
    typeof(LuaGameFramework),
    // ...
};
```

### Lua脚本路径配置
- **Resources路径**: `Resources/LuaScripts/`
- **StreamingAssets路径**: `StreamingAssets/LuaScripts/`
- **主脚本**: `Main.lua`

## 常用API参考

### 游戏状态管理
```lua
-- 状态常量
local GAME_STATE_NONE = 0
local GAME_STATE_INITIALIZE = 1
local GAME_STATE_LOADING = 2
local GAME_STATE_MAIN_MENU = 3
local GAME_STATE_IN_GAME = 4
local GAME_STATE_PAUSED = 5

-- 切换状态
GameFramework.GameState.ChangeState(GAME_STATE_IN_GAME)

-- 获取当前状态
local currentState = GameFramework.GameState.CurrentState

-- 监听状态改变
GameFramework.GameState.OnStateChanged(function(oldState, newState)
    print("状态改变: " .. oldState .. " -> " .. newState)
end)
```

### 输入管理
```lua
-- 输入动作常量
local ACTION_MOVE = 1
local ACTION_LOOK = 2
local ACTION_ATTACK = 3
local ACTION_JUMP = 5

-- 获取输入
local moveVector = GameFramework.Input.GetVector2(ACTION_MOVE)
local isJumping = GameFramework.Input.GetButtonDown(ACTION_JUMP)

-- 检查设备类型
if Input.IsKeyboardMouse() then
    print("使用键盘鼠标")
elseif Input.IsGamepad() then
    print("使用手柄")
end
```

### 资源管理
```lua
-- 异步加载预制体
GameFramework.Resource.LoadGameObjectAsync("Prefabs/Player", function(prefab)
    if prefab then
        local instance = GameObject.Instantiate(prefab)
        print("预制体实例化成功")
    end
end)

-- 加载纹理
GameFramework.Resource.LoadTextureAsync("Textures/PlayerAvatar", function(texture)
    if texture then
        -- 使用纹理
    end
end)

-- 加载配置文件
Resource.LoadConfig("PlayerConfig", function(content)
    local config = json.decode(content)
    -- 使用配置数据
end)
```

## 调试和优化

### 热重载
```csharp
// 在编辑器中重新加载Lua脚本
luaManager.ReloadLuaScripts();
```

### 性能监控
```lua
-- 获取协程数量
local coroutineCount = Coroutine.GetRunningCoroutineCount()
print("运行中的协程数量: " .. coroutineCount)

-- 获取事件监听器数量
local listenerCount = Event.GetListenerCount("PlayerDie")
print("PlayerDie事件监听器数量: " .. listenerCount)
```

### 内存管理
```csharp
// 定期执行Lua垃圾回收
luaEnv.Tick();

// 清理未使用的资源
Resource.CleanupUnusedResources();
```

## 最佳实践

1. **模块化设计**: 将Lua代码按功能模块组织
2. **事件驱动**: 使用事件系统实现模块间通信
3. **资源管理**: 及时卸载不需要的资源
4. **错误处理**: 在Lua代码中添加适当的错误处理
5. **性能优化**: 避免在Update中进行复杂计算

## 示例项目结构

```
Assets/
├── Script/
│   └── XLua/
│       ├── XLuaConfig.cs
│       ├── LuaManager.cs
│       ├── LuaGameFramework.cs
│       └── Examples/
└── Resources/
    └── LuaScripts/
        ├── Main.lua
        ├── UI/
        │   ├── MainMenuUI.lua
        │   └── GameHUD.lua
        ├── States/
        │   ├── MainMenuState.lua
        │   └── GameState.lua
        └── Utils/
            ├── MathUtils.lua
            └── StringUtils.lua
```

## 故障排除

### 常见问题

1. **Lua脚本找不到**: 检查脚本路径和文件名
2. **类型转换错误**: 确保C#类型已在XLuaConfig中配置
3. **内存泄漏**: 及时清理事件监听器和协程
4. **性能问题**: 避免频繁的C#与Lua调用

### 调试技巧

1. 使用`print()`输出调试信息
2. 利用Unity Console查看Lua错误
3. 使用热重载快速测试修改
4. 监控协程和定时器的数量

通过这个xLua集成，你可以使用Lua脚本来实现游戏逻辑，同时保持与Unity游戏框架的完整集成！
