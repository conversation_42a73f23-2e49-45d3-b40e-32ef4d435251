using System;
using System.Collections.Generic;
using UnityEngine;
using XLua;
using GameFramework.Core;

namespace GameFramework.XLua
{
    /// <summary>
    /// Lua输入管理器封装
    /// </summary>
    public class LuaInputManager
    {
        private static LuaInputManager instance;
        private Dictionary<string, ILuaInputHandler> luaInputHandlers = new Dictionary<string, ILuaInputHandler>();

        public static LuaInputManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new LuaInputManager();
                }
                return instance;
            }
        }

        /// <summary>
        /// 当前输入设备类型
        /// </summary>
        public int CurrentDeviceType
        {
            get
            {
                return (int)(GameFramework.Core.GameFramework.Input?.CurrentDeviceType ?? InputDeviceType.None);
            }
        }

        /// <summary>
        /// 输入是否启用
        /// </summary>
        public bool InputEnabled
        {
            get
            {
                return GameFramework.Core.GameFramework.Input?.InputEnabled ?? false;
            }
            set
            {
                if (GameFramework.Core.GameFramework.Input != null)
                {
                    GameFramework.Core.GameFramework.Input.InputEnabled = value;
                }
            }
        }

        /// <summary>
        /// 获取按钮输入状态
        /// </summary>
        /// <param name="actionType">动作类型</param>
        /// <returns>是否按下</returns>
        public bool GetButton(int actionType)
        {
            InputActionType action = (InputActionType)actionType;
            return GameFramework.Core.GameFramework.Input?.GetButton(action) ?? false;
        }

        /// <summary>
        /// 获取按钮按下事件
        /// </summary>
        /// <param name="actionType">动作类型</param>
        /// <returns>是否刚按下</returns>
        public bool GetButtonDown(int actionType)
        {
            InputActionType action = (InputActionType)actionType;
            return GameFramework.Core.GameFramework.Input?.GetButtonDown(action) ?? false;
        }

        /// <summary>
        /// 获取按钮释放事件
        /// </summary>
        /// <param name="actionType">动作类型</param>
        /// <returns>是否刚释放</returns>
        public bool GetButtonUp(int actionType)
        {
            InputActionType action = (InputActionType)actionType;
            return GameFramework.Core.GameFramework.Input?.GetButtonUp(action) ?? false;
        }

        /// <summary>
        /// 获取轴输入值
        /// </summary>
        /// <param name="actionType">动作类型</param>
        /// <returns>轴值</returns>
        public float GetAxis(int actionType)
        {
            InputActionType action = (InputActionType)actionType;
            return GameFramework.Core.GameFramework.Input?.GetAxis(action) ?? 0f;
        }

        /// <summary>
        /// 获取向量输入值
        /// </summary>
        /// <param name="actionType">动作类型</param>
        /// <returns>向量值</returns>
        public Vector2 GetVector2(int actionType)
        {
            InputActionType action = (InputActionType)actionType;
            return GameFramework.Core.GameFramework.Input?.GetVector2(action) ?? Vector2.zero;
        }

        /// <summary>
        /// 启用输入动作映射
        /// </summary>
        /// <param name="mapName">映射名称</param>
        public void EnableActionMap(string mapName)
        {
            GameFramework.Core.GameFramework.Input?.EnableActionMap(mapName);
        }

        /// <summary>
        /// 禁用输入动作映射
        /// </summary>
        /// <param name="mapName">映射名称</param>
        public void DisableActionMap(string mapName)
        {
            GameFramework.Core.GameFramework.Input?.DisableActionMap(mapName);
        }

        /// <summary>
        /// 切换输入动作映射
        /// </summary>
        /// <param name="mapName">映射名称</param>
        public void SwitchActionMap(string mapName)
        {
            GameFramework.Core.GameFramework.Input?.SwitchActionMap(mapName);
        }

        /// <summary>
        /// 注册Lua输入处理器
        /// </summary>
        /// <param name="handlerName">处理器名称</param>
        /// <param name="handler">Lua输入处理器</param>
        public void RegisterLuaInputHandler(string handlerName, ILuaInputHandler handler)
        {
            if (luaInputHandlers.ContainsKey(handlerName))
            {
                Debug.LogWarning($"[LuaInputManager] 替换Lua输入处理器: {handlerName}");
            }

            luaInputHandlers[handlerName] = handler;
            Debug.Log($"[LuaInputManager] 注册Lua输入处理器: {handlerName}");
        }

        /// <summary>
        /// 注销Lua输入处理器
        /// </summary>
        /// <param name="handlerName">处理器名称</param>
        public void UnregisterLuaInputHandler(string handlerName)
        {
            if (luaInputHandlers.ContainsKey(handlerName))
            {
                luaInputHandlers.Remove(handlerName);
                Debug.Log($"[LuaInputManager] 注销Lua输入处理器: {handlerName}");
            }
        }

        /// <summary>
        /// 注册输入动作事件
        /// </summary>
        /// <param name="callback">回调函数</param>
        public void OnInputAction(System.Action<InputActionType, InputState, object> callback)
        {
            if (GameFramework.Core.GameFramework.Input != null)
            {
                GameFramework.Core.GameFramework.Input.OnInputAction += callback;
            }
        }

        /// <summary>
        /// 注销输入动作事件
        /// </summary>
        /// <param name="callback">回调函数</param>
        public void OffInputAction(System.Action<InputActionType, InputState, object> callback)
        {
            if (GameFramework.Core.GameFramework.Input != null)
            {
                GameFramework.Core.GameFramework.Input.OnInputAction -= callback;
            }
        }

        /// <summary>
        /// 注册设备切换事件
        /// </summary>
        /// <param name="callback">回调函数</param>
        public void OnDeviceChanged(System.Action<InputDeviceType> callback)
        {
            if (GameFramework.Core.GameFramework.Input != null)
            {
                GameFramework.Core.GameFramework.Input.OnDeviceChanged += callback;
            }
        }

        /// <summary>
        /// 注销设备切换事件
        /// </summary>
        /// <param name="callback">回调函数</param>
        public void OffDeviceChanged(System.Action<InputDeviceType> callback)
        {
            if (GameFramework.Core.GameFramework.Input != null)
            {
                GameFramework.Core.GameFramework.Input.OnDeviceChanged -= callback;
            }
        }

        /// <summary>
        /// 分发输入事件到Lua处理器
        /// </summary>
        /// <param name="actionType">动作类型</param>
        /// <param name="state">输入状态</param>
        /// <param name="value">输入值</param>
        public void DispatchInputToLua(InputActionType actionType, InputState state, object value)
        {
            foreach (var handler in luaInputHandlers.Values)
            {
                try
                {
                    handler.OnInputAction(actionType, state, value);
                }
                catch (Exception e)
                {
                    Debug.LogError($"[LuaInputManager] Lua输入处理器异常: {e.Message}");
                }
            }
        }

        /// <summary>
        /// 分发设备切换事件到Lua处理器
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        public void DispatchDeviceChangeToLua(InputDeviceType deviceType)
        {
            foreach (var handler in luaInputHandlers.Values)
            {
                try
                {
                    handler.OnDeviceChanged(deviceType);
                }
                catch (Exception e)
                {
                    Debug.LogError($"[LuaInputManager] Lua设备切换处理器异常: {e.Message}");
                }
            }
        }

        /// <summary>
        /// 获取设备类型名称
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>设备名称</returns>
        public string GetDeviceTypeName(int deviceType)
        {
            InputDeviceType type = (InputDeviceType)deviceType;
            return type.ToString();
        }

        /// <summary>
        /// 获取动作类型名称
        /// </summary>
        /// <param name="actionType">动作类型</param>
        /// <returns>动作名称</returns>
        public string GetActionTypeName(int actionType)
        {
            InputActionType type = (InputActionType)actionType;
            return type.ToString();
        }

        /// <summary>
        /// 检查是否为指定设备类型
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>是否为指定设备</returns>
        public bool IsDeviceType(int deviceType)
        {
            return CurrentDeviceType == deviceType;
        }
    }

    /// <summary>
    /// Lua输入管理辅助类
    /// </summary>
    public static class LuaInputHelper
    {
        // 输入动作类型常量
        public const int ACTION_MOVE = (int)InputActionType.Move;
        public const int ACTION_LOOK = (int)InputActionType.Look;
        public const int ACTION_ATTACK = (int)InputActionType.Attack;
        public const int ACTION_INTERACT = (int)InputActionType.Interact;
        public const int ACTION_JUMP = (int)InputActionType.Jump;
        public const int ACTION_CROUCH = (int)InputActionType.Crouch;
        public const int ACTION_SPRINT = (int)InputActionType.Sprint;
        public const int ACTION_PREVIOUS = (int)InputActionType.Previous;
        public const int ACTION_NEXT = (int)InputActionType.Next;
        public const int ACTION_NAVIGATE = (int)InputActionType.Navigate;
        public const int ACTION_SUBMIT = (int)InputActionType.Submit;
        public const int ACTION_CANCEL = (int)InputActionType.Cancel;
        public const int ACTION_POINT = (int)InputActionType.Point;
        public const int ACTION_CLICK = (int)InputActionType.Click;
        public const int ACTION_RIGHT_CLICK = (int)InputActionType.RightClick;
        public const int ACTION_MIDDLE_CLICK = (int)InputActionType.MiddleClick;
        public const int ACTION_SCROLL_WHEEL = (int)InputActionType.ScrollWheel;

        // 设备类型常量
        public const int DEVICE_NONE = (int)InputDeviceType.None;
        public const int DEVICE_KEYBOARD_MOUSE = (int)InputDeviceType.KeyboardMouse;
        public const int DEVICE_GAMEPAD = (int)InputDeviceType.Gamepad;
        public const int DEVICE_TOUCH = (int)InputDeviceType.Touch;
        public const int DEVICE_JOYSTICK = (int)InputDeviceType.Joystick;
        public const int DEVICE_XR = (int)InputDeviceType.XR;

        /// <summary>
        /// 检查移动输入
        /// </summary>
        /// <returns>移动向量</returns>
        public static Vector2 GetMoveInput()
        {
            return LuaGameFramework.Input.GetVector2(ACTION_MOVE);
        }

        /// <summary>
        /// 检查视角输入
        /// </summary>
        /// <returns>视角向量</returns>
        public static Vector2 GetLookInput()
        {
            return LuaGameFramework.Input.GetVector2(ACTION_LOOK);
        }

        /// <summary>
        /// 检查跳跃输入
        /// </summary>
        /// <returns>是否按下跳跃</returns>
        public static bool GetJumpInput()
        {
            return LuaGameFramework.Input.GetButtonDown(ACTION_JUMP);
        }

        /// <summary>
        /// 检查攻击输入
        /// </summary>
        /// <returns>是否按下攻击</returns>
        public static bool GetAttackInput()
        {
            return LuaGameFramework.Input.GetButtonDown(ACTION_ATTACK);
        }

        /// <summary>
        /// 检查交互输入
        /// </summary>
        /// <returns>是否按下交互</returns>
        public static bool GetInteractInput()
        {
            return LuaGameFramework.Input.GetButtonDown(ACTION_INTERACT);
        }

        /// <summary>
        /// 检查冲刺输入
        /// </summary>
        /// <returns>是否按住冲刺</returns>
        public static bool GetSprintInput()
        {
            return LuaGameFramework.Input.GetButton(ACTION_SPRINT);
        }

        /// <summary>
        /// 检查取消输入
        /// </summary>
        /// <returns>是否按下取消</returns>
        public static bool GetCancelInput()
        {
            return LuaGameFramework.Input.GetButtonDown(ACTION_CANCEL);
        }

        /// <summary>
        /// 检查确认输入
        /// </summary>
        /// <returns>是否按下确认</returns>
        public static bool GetSubmitInput()
        {
            return LuaGameFramework.Input.GetButtonDown(ACTION_SUBMIT);
        }

        /// <summary>
        /// 切换到游戏输入模式
        /// </summary>
        public static void SwitchToGameplayInput()
        {
            LuaGameFramework.Input.SwitchActionMap("Player");
        }

        /// <summary>
        /// 切换到UI输入模式
        /// </summary>
        public static void SwitchToUIInput()
        {
            LuaGameFramework.Input.SwitchActionMap("UI");
        }

        /// <summary>
        /// 禁用所有输入
        /// </summary>
        public static void DisableAllInput()
        {
            LuaGameFramework.Input.InputEnabled = false;
        }

        /// <summary>
        /// 启用所有输入
        /// </summary>
        public static void EnableAllInput()
        {
            LuaGameFramework.Input.InputEnabled = true;
        }

        /// <summary>
        /// 检查是否为键盘鼠标输入
        /// </summary>
        /// <returns>是否为键盘鼠标</returns>
        public static bool IsKeyboardMouse()
        {
            return LuaGameFramework.Input.IsDeviceType(DEVICE_KEYBOARD_MOUSE);
        }

        /// <summary>
        /// 检查是否为手柄输入
        /// </summary>
        /// <returns>是否为手柄</returns>
        public static bool IsGamepad()
        {
            return LuaGameFramework.Input.IsDeviceType(DEVICE_GAMEPAD);
        }

        /// <summary>
        /// 检查是否为触屏输入
        /// </summary>
        /// <returns>是否为触屏</returns>
        public static bool IsTouch()
        {
            return LuaGameFramework.Input.IsDeviceType(DEVICE_TOUCH);
        }
    }
}
