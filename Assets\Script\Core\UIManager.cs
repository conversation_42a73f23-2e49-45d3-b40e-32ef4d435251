using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace GameFramework.Core
{
    /// <summary>
    /// UI管理器
    /// 管理全屏UI、弹出式UI的创建、显示、隐藏和销毁
    /// </summary>
    public class UIManager : MonoBehaviour, IUIManager
    {
        [Header("UI管理器设置")]
        [SerializeField] private Canvas uiCanvas;
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private string uiResourcePath = "UI/";

        [Header("UI层级设置")]
        [SerializeField] private Transform backgroundLayer;
        [SerializeField] private Transform normalLayer;
        [SerializeField] private Transform fixedLayer;
        [SerializeField] private Transform popupLayer;
        [SerializeField] private Transform guideLayer;
        [SerializeField] private Transform noticeLayer;
        [SerializeField] private Transform topLayer;

        private Dictionary<string, GameObject> uiInstances = new Dictionary<string, GameObject>();
        private Dictionary<string, UIInfo> uiInfos = new Dictionary<string, UIInfo>();
        private Stack<string> uiHistory = new Stack<string>();

        public string Name => "UIManager";
        public bool IsInitialized { get; private set; }
        public Transform UIRoot => uiCanvas?.transform;

        private void Awake()
        {
            // 确保单例
            if (FindObjectsOfType<UIManager>().Length > 1)
            {
                Destroy(gameObject);
                return;
            }
            DontDestroyOnLoad(gameObject);
        }

        private void Start()
        {
            Initialize();
        }

        public void Initialize()
        {
            if (IsInitialized)
                return;

            LogDebug("初始化UI管理器");

            // 创建UI Canvas
            if (uiCanvas == null)
            {
                CreateUICanvas();
            }

            // 创建UI层级
            CreateUILayers();

            IsInitialized = true;
            LogDebug("UI管理器初始化完成");
        }

        public void Update()
        {
            // UI管理器更新逻辑
        }

        public void Destroy()
        {
            LogDebug("销毁UI管理器");

            // 销毁所有UI
            foreach (var ui in uiInstances.Values)
            {
                if (ui != null)
                {
                    Destroy(ui);
                }
            }

            uiInstances.Clear();
            uiInfos.Clear();
            uiHistory.Clear();

            IsInitialized = false;
        }

        public T CreateUI<T>(string uiName, UIType uiType = UIType.Normal, UILayer layer = UILayer.Normal) where T : MonoBehaviour
        {
            if (string.IsNullOrEmpty(uiName))
            {
                LogDebug("UI名称不能为空");
                return null;
            }

            if (uiInstances.ContainsKey(uiName))
            {
                LogDebug($"UI已存在: {uiName}");
                return uiInstances[uiName].GetComponent<T>();
            }

            LogDebug($"创建UI: {uiName}, 类型: {uiType}, 层级: {layer}");

            try
            {
                // 加载UI预制体
                GameObject uiPrefab = Resources.Load<GameObject>(uiResourcePath + uiName);
                if (uiPrefab == null)
                {
                    LogDebug($"找不到UI预制体: {uiResourcePath + uiName}");
                    return null;
                }

                // 实例化UI
                Transform parentLayer = GetUILayer(layer);
                GameObject uiInstance = Instantiate(uiPrefab, parentLayer);
                uiInstance.name = uiName;

                // 设置UI信息
                UIInfo uiInfo = new UIInfo
                {
                    name = uiName,
                    type = uiType,
                    layer = layer,
                    gameObject = uiInstance,
                    isVisible = true
                };

                uiInstances[uiName] = uiInstance;
                uiInfos[uiName] = uiInfo;

                // 添加到历史记录
                if (uiType == UIType.FullScreen)
                {
                    uiHistory.Push(uiName);
                }

                T component = uiInstance.GetComponent<T>();
                if (component == null)
                {
                    component = uiInstance.AddComponent<T>();
                }

                LogDebug($"UI创建成功: {uiName}");
                return component;
            }
            catch (Exception e)
            {
                LogDebug($"创建UI失败: {uiName}, 错误: {e.Message}");
                return null;
            }
        }

        public void ShowUI(string uiName)
        {
            if (!uiInstances.ContainsKey(uiName))
            {
                LogDebug($"UI不存在: {uiName}");
                return;
            }

            GameObject uiInstance = uiInstances[uiName];
            if (uiInstance != null)
            {
                uiInstance.SetActive(true);
                uiInfos[uiName].isVisible = true;
                LogDebug($"显示UI: {uiName}");

                // 如果是全屏UI，隐藏其他全屏UI
                if (uiInfos[uiName].type == UIType.FullScreen)
                {
                    HideOtherFullScreenUIs(uiName);
                }
            }
        }

        public void HideUI(string uiName)
        {
            if (!uiInstances.ContainsKey(uiName))
            {
                LogDebug($"UI不存在: {uiName}");
                return;
            }

            GameObject uiInstance = uiInstances[uiName];
            if (uiInstance != null)
            {
                uiInstance.SetActive(false);
                uiInfos[uiName].isVisible = false;
                LogDebug($"隐藏UI: {uiName}");
            }
        }

        public void DestroyUI(string uiName)
        {
            if (!uiInstances.ContainsKey(uiName))
            {
                LogDebug($"UI不存在: {uiName}");
                return;
            }

            GameObject uiInstance = uiInstances[uiName];
            if (uiInstance != null)
            {
                Destroy(uiInstance);
            }

            uiInstances.Remove(uiName);
            uiInfos.Remove(uiName);

            LogDebug($"销毁UI: {uiName}");
        }

        public T GetUI<T>(string uiName) where T : MonoBehaviour
        {
            if (!uiInstances.ContainsKey(uiName))
            {
                return null;
            }

            return uiInstances[uiName].GetComponent<T>();
        }

        /// <summary>
        /// 返回上一个UI
        /// </summary>
        public void GoBackToPreviousUI()
        {
            if (uiHistory.Count > 1)
            {
                string currentUI = uiHistory.Pop();
                string previousUI = uiHistory.Peek();
                
                HideUI(currentUI);
                ShowUI(previousUI);
            }
        }

        /// <summary>
        /// 获取UI信息
        /// </summary>
        public UIInfo GetUIInfo(string uiName)
        {
            return uiInfos.ContainsKey(uiName) ? uiInfos[uiName] : null;
        }

        /// <summary>
        /// 创建UI Canvas
        /// </summary>
        private void CreateUICanvas()
        {
            GameObject canvasObject = new GameObject("UICanvas");
            canvasObject.transform.SetParent(transform);

            uiCanvas = canvasObject.AddComponent<Canvas>();
            uiCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
            uiCanvas.sortingOrder = 0;

            CanvasScaler scaler = canvasObject.AddComponent<CanvasScaler>();
            scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            scaler.referenceResolution = new Vector2(1920, 1080);
            scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
            scaler.matchWidthOrHeight = 0.5f;

            canvasObject.AddComponent<GraphicRaycaster>();

            LogDebug("创建UI Canvas");
        }

        /// <summary>
        /// 创建UI层级
        /// </summary>
        private void CreateUILayers()
        {
            backgroundLayer = CreateLayer("BackgroundLayer", 0);
            normalLayer = CreateLayer("NormalLayer", 100);
            fixedLayer = CreateLayer("FixedLayer", 200);
            popupLayer = CreateLayer("PopupLayer", 300);
            guideLayer = CreateLayer("GuideLayer", 400);
            noticeLayer = CreateLayer("NoticeLayer", 500);
            topLayer = CreateLayer("TopLayer", 600);

            LogDebug("创建UI层级");
        }

        /// <summary>
        /// 创建UI层
        /// </summary>
        private Transform CreateLayer(string layerName, int sortingOrder)
        {
            GameObject layerObject = new GameObject(layerName);
            layerObject.transform.SetParent(uiCanvas.transform, false);

            Canvas layerCanvas = layerObject.AddComponent<Canvas>();
            layerCanvas.overrideSorting = true;
            layerCanvas.sortingOrder = sortingOrder;

            GraphicRaycaster raycaster = layerObject.AddComponent<GraphicRaycaster>();

            return layerObject.transform;
        }

        /// <summary>
        /// 获取UI层级
        /// </summary>
        private Transform GetUILayer(UILayer layer)
        {
            return layer switch
            {
                UILayer.Background => backgroundLayer,
                UILayer.Normal => normalLayer,
                UILayer.Fixed => fixedLayer,
                UILayer.Popup => popupLayer,
                UILayer.Guide => guideLayer,
                UILayer.Notice => noticeLayer,
                UILayer.Top => topLayer,
                _ => normalLayer
            };
        }

        /// <summary>
        /// 隐藏其他全屏UI
        /// </summary>
        private void HideOtherFullScreenUIs(string excludeUI)
        {
            foreach (var uiInfo in uiInfos.Values)
            {
                if (uiInfo.name != excludeUI && uiInfo.type == UIType.FullScreen && uiInfo.isVisible)
                {
                    HideUI(uiInfo.name);
                }
            }
        }

        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[UIManager] {message}");
            }
        }
    }

    /// <summary>
    /// UI信息类
    /// </summary>
    [Serializable]
    public class UIInfo
    {
        public string name;
        public UIType type;
        public UILayer layer;
        public GameObject gameObject;
        public bool isVisible;
    }
}
