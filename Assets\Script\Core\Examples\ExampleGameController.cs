using UnityEngine;
using GameFramework.Core;

namespace GameFramework.Examples
{
    /// <summary>
    /// 游戏控制器示例
    /// 展示如何使用游戏框架的各个管理器
    /// </summary>
    public class ExampleGameController : MonoBehaviour
    {
        [Header("示例设置")]
        [SerializeField] private bool enableDebugLog = true;

        private void Start()
        {
            LogDebug("游戏控制器示例启动");

            // 等待框架初始化完成
            if (Core.GameFramework.Instance.IsInitialized)
            {
                OnFrameworkReady();
            }
            else
            {
                Core.GameFramework.OnFrameworkInitialized += OnFrameworkReady;
            }
        }

        private void OnFrameworkReady()
        {
            LogDebug("框架初始化完成");

            // 注册游戏状态改变事件
            if (Core.GameFramework.GameState != null)
            {
                Core.GameFramework.GameState.OnStateChanged += OnGameStateChanged;
            }
        }

        private void Update()
        {
            // 简单的输入检测示例
            if (Input.GetKeyDown(KeyCode.Alpha1))
            {
                LogDebug("按下数字键1 - 切换到主菜单状态");
                Core.GameFramework.GameState?.ChangeState(GameState.MainMenu);
            }
            else if (Input.GetKeyDown(KeyCode.Alpha2))
            {
                LogDebug("按下数字键2 - 切换到游戏状态");
                Core.GameFramework.GameState?.ChangeState(GameState.InGame);
            }
            else if (Input.GetKeyDown(KeyCode.Alpha3))
            {
                LogDebug("按下数字键3 - 切换到暂停状态");
                Core.GameFramework.GameState?.ChangeState(GameState.Paused);
            }
        }

        private void OnGameStateChanged(GameState oldState, GameState newState)
        {
            LogDebug($"游戏状态改变: {oldState} -> {newState}");
        }

        private void OnDestroy()
        {
            // 清理事件
            if (Core.GameFramework.GameState != null)
            {
                Core.GameFramework.GameState.OnStateChanged -= OnGameStateChanged;
            }
            Core.GameFramework.OnFrameworkInitialized -= OnFrameworkReady;
        }

        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[ExampleGameController] {message}");
            }
        }

        private void OnGUI()
        {
            // 显示简单的状态信息
            GUILayout.BeginArea(new Rect(10, 10, 300, 150));
            GUILayout.Label("游戏框架示例控制器");

            if (Core.GameFramework.GameState != null)
            {
                GUILayout.Label($"当前状态: {Core.GameFramework.GameState.CurrentState}");
            }

            GUILayout.Space(10);
            GUILayout.Label("操作说明:");
            GUILayout.Label("1 - 主菜单状态");
            GUILayout.Label("2 - 游戏状态");
            GUILayout.Label("3 - 暂停状态");
            GUILayout.EndArea();
        }
    }
}
