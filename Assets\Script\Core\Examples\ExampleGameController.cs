using UnityEngine;
using GameFramework.Core;

namespace GameFramework.Examples
{
    /// <summary>
    /// 游戏控制器示例
    /// 展示如何使用游戏框架的各个管理器
    /// </summary>
    public class ExampleGameController : MonoBehaviour
    {
        [Header("示例设置")]
        [SerializeField] private string testSceneName = "TestScene";
        [SerializeField] private string testUIName = "TestUI";
        [SerializeField] private string testResourcePath = "TestPrefab";

        private void Start()
        {
            // 等待框架初始化完成
            if (GameFramework.Instance.IsInitialized)
            {
                OnFrameworkReady();
            }
            else
            {
                GameFramework.OnFrameworkInitialized += OnFrameworkReady;
            }
        }

        private void OnFrameworkReady()
        {
            Debug.Log("[ExampleGameController] 框架初始化完成，开始示例");

            // 注册游戏状态改变事件
            if (GameFramework.GameState != null)
            {
                GameFramework.GameState.OnStateChanged += OnGameStateChanged;
            }

            // 注册场景加载事件
            if (GameFramework.Scene != null)
            {
                GameFramework.Scene.OnSceneLoaded += OnSceneLoaded;
                GameFramework.Scene.OnSceneLoadProgress += OnSceneLoadProgress;
            }
        }

        private void Update()
        {
            // 示例输入处理
            HandleInput();
        }

        private void HandleInput()
        {
            // 按键示例
            if (Input.GetKeyDown(KeyCode.Alpha1))
            {
                // 切换到主菜单状态
                GameFramework.GameState?.ChangeState(GameState.MainMenu);
            }
            else if (Input.GetKeyDown(KeyCode.Alpha2))
            {
                // 切换到游戏状态
                GameFramework.GameState?.ChangeState(GameState.InGame);
            }
            else if (Input.GetKeyDown(KeyCode.Alpha3))
            {
                // 暂停/恢复游戏
                if (GameFramework.GameState?.CurrentState == GameState.InGame)
                {
                    GameFramework.GameState.ChangeState(GameState.Paused);
                }
                else if (GameFramework.GameState?.CurrentState == GameState.Paused)
                {
                    GameFramework.GameState.ReturnToPreviousState();
                }
            }
            else if (Input.GetKeyDown(KeyCode.L))
            {
                // 加载场景示例
                LoadSceneExample();
            }
            else if (Input.GetKeyDown(KeyCode.U))
            {
                // UI操作示例
                UIExample();
            }
            else if (Input.GetKeyDown(KeyCode.R))
            {
                // 资源加载示例
                ResourceExample();
            }
            else if (Input.GetKeyDown(KeyCode.I))
            {
                // 输入系统示例
                InputExample();
            }
        }

        /// <summary>
        /// 场景加载示例
        /// </summary>
        private void LoadSceneExample()
        {
            Debug.Log("[ExampleGameController] 场景加载示例");

            if (GameFramework.Scene != null)
            {
                // 异步加载场景
                GameFramework.Scene.LoadSceneAsync(testSceneName);
            }
        }

        /// <summary>
        /// UI操作示例
        /// </summary>
        private void UIExample()
        {
            Debug.Log("[ExampleGameController] UI操作示例");

            if (GameFramework.UI != null)
            {
                // 检查UI是否存在
                var testUI = GameFramework.UI.GetUI<MonoBehaviour>(testUIName);
                if (testUI == null)
                {
                    // 创建UI
                    testUI = GameFramework.UI.CreateUI<MonoBehaviour>(testUIName, UIType.Popup, UILayer.Popup);
                    if (testUI != null)
                    {
                        Debug.Log($"[ExampleGameController] 创建UI成功: {testUIName}");
                    }
                }
                else
                {
                    // 切换UI显示状态
                    var uiInfo = GameFramework.UI.GetUIInfo(testUIName);
                    if (uiInfo != null)
                    {
                        if (uiInfo.isVisible)
                        {
                            GameFramework.UI.HideUI(testUIName);
                            Debug.Log($"[ExampleGameController] 隐藏UI: {testUIName}");
                        }
                        else
                        {
                            GameFramework.UI.ShowUI(testUIName);
                            Debug.Log($"[ExampleGameController] 显示UI: {testUIName}");
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 资源加载示例
        /// </summary>
        private void ResourceExample()
        {
            Debug.Log("[ExampleGameController] 资源加载示例");

            if (GameFramework.Resource != null)
            {
                // 同步加载资源
                GameObject prefab = GameFramework.Resource.LoadResource<GameObject>(testResourcePath);
                if (prefab != null)
                {
                    Debug.Log($"[ExampleGameController] 同步加载资源成功: {testResourcePath}");
                    
                    // 实例化预制体
                    GameObject instance = Instantiate(prefab);
                    instance.name = "ExampleInstance";
                }
                else
                {
                    Debug.Log($"[ExampleGameController] 同步加载资源失败: {testResourcePath}");
                    
                    // 尝试异步加载
                    GameFramework.Resource.LoadResourceAsync<GameObject>(testResourcePath, (resource) =>
                    {
                        if (resource != null)
                        {
                            Debug.Log($"[ExampleGameController] 异步加载资源成功: {testResourcePath}");
                            GameObject instance = Instantiate(resource);
                            instance.name = "ExampleInstanceAsync";
                        }
                        else
                        {
                            Debug.Log($"[ExampleGameController] 异步加载资源失败: {testResourcePath}");
                        }
                    });
                }
            }
        }

        /// <summary>
        /// 输入系统示例
        /// </summary>
        private void InputExample()
        {
            Debug.Log("[ExampleGameController] 输入系统示例");

            if (GameFramework.Input != null)
            {
                // 检查当前输入设备
                Debug.Log($"[ExampleGameController] 当前输入设备: {GameFramework.Input.CurrentDeviceType}");

                // 检查各种输入状态
                bool movePressed = GameFramework.Input.GetButton(InputActionType.Move);
                Vector2 moveVector = GameFramework.Input.GetVector2(InputActionType.Move);
                bool jumpPressed = GameFramework.Input.GetButtonDown(InputActionType.Jump);

                Debug.Log($"[ExampleGameController] 移动按下: {movePressed}, 移动向量: {moveVector}");
                Debug.Log($"[ExampleGameController] 跳跃按下: {jumpPressed}");

                // 切换输入映射示例
                if (GameFramework.Input.GetButtonDown(InputActionType.Cancel))
                {
                    GameFramework.Input.SwitchActionMap("UI");
                    Debug.Log("[ExampleGameController] 切换到UI输入映射");
                }
            }
        }

        /// <summary>
        /// 下载资源示例
        /// </summary>
        private void DownloadExample()
        {
            Debug.Log("[ExampleGameController] 下载资源示例");

            if (GameFramework.Resource != null)
            {
                string url = "https://example.com/test.png";
                string savePath = Application.persistentDataPath + "/test.png";

                GameFramework.Resource.DownloadResource(url, savePath,
                    onProgress: (progress) =>
                    {
                        Debug.Log($"[ExampleGameController] 下载进度: {progress:P}");
                    },
                    onComplete: (success) =>
                    {
                        if (success)
                        {
                            Debug.Log($"[ExampleGameController] 下载完成: {savePath}");
                        }
                        else
                        {
                            Debug.Log($"[ExampleGameController] 下载失败: {url}");
                        }
                    });
            }
        }

        /// <summary>
        /// 游戏状态改变回调
        /// </summary>
        private void OnGameStateChanged(GameState oldState, GameState newState)
        {
            Debug.Log($"[ExampleGameController] 游戏状态改变: {oldState} -> {newState}");

            // 根据状态执行相应逻辑
            switch (newState)
            {
                case GameState.MainMenu:
                    Debug.Log("[ExampleGameController] 进入主菜单，可以显示主菜单UI");
                    break;
                case GameState.InGame:
                    Debug.Log("[ExampleGameController] 进入游戏，可以显示游戏UI");
                    break;
                case GameState.Paused:
                    Debug.Log("[ExampleGameController] 游戏暂停，可以显示暂停菜单");
                    break;
                case GameState.GameOver:
                    Debug.Log("[ExampleGameController] 游戏结束，可以显示结算界面");
                    break;
            }
        }

        /// <summary>
        /// 场景加载完成回调
        /// </summary>
        private void OnSceneLoaded(string sceneName)
        {
            Debug.Log($"[ExampleGameController] 场景加载完成: {sceneName}");
        }

        /// <summary>
        /// 场景加载进度回调
        /// </summary>
        private void OnSceneLoadProgress(float progress)
        {
            Debug.Log($"[ExampleGameController] 场景加载进度: {progress:P}");
        }

        private void OnDestroy()
        {
            // 注销事件
            if (GameFramework.GameState != null)
            {
                GameFramework.GameState.OnStateChanged -= OnGameStateChanged;
            }

            if (GameFramework.Scene != null)
            {
                GameFramework.Scene.OnSceneLoaded -= OnSceneLoaded;
                GameFramework.Scene.OnSceneLoadProgress -= OnSceneLoadProgress;
            }

            GameFramework.OnFrameworkInitialized -= OnFrameworkReady;
        }

        private void OnGUI()
        {
            // 简单的GUI显示当前状态和操作提示
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.Label("游戏框架示例控制器");
            GUILayout.Label($"当前状态: {GameFramework.GameState?.CurrentState}");
            GUILayout.Label($"当前场景: {GameFramework.Scene?.CurrentSceneName}");
            GUILayout.Space(10);
            GUILayout.Label("操作说明:");
            GUILayout.Label("1 - 主菜单状态");
            GUILayout.Label("2 - 游戏状态");
            GUILayout.Label("3 - 暂停/恢复");
            GUILayout.Label("L - 加载场景");
            GUILayout.Label("U - UI操作");
            GUILayout.Label("R - 资源加载");
            GUILayout.Label("I - 输入系统示例");
            GUILayout.EndArea();
        }
    }
}
