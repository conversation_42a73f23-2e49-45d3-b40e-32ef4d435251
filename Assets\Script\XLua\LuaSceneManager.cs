using System;
using System.Collections.Generic;
using UnityEngine;
using XLua;
using GameFramework.Core;

namespace GameFramework.XLua
{
    /// <summary>
    /// Lua场景管理器封装
    /// </summary>
    public class LuaSceneManager
    {
        private static LuaSceneManager instance;

        public static LuaSceneManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new LuaSceneManager();
                }
                return instance;
            }
        }

        /// <summary>
        /// 当前场景名称
        /// </summary>
        public string CurrentSceneName
        {
            get
            {
                return GameFramework.Core.GameFramework.Scene?.CurrentSceneName ?? "";
            }
        }

        /// <summary>
        /// 加载场景
        /// </summary>
        /// <param name="sceneName">场景名称</param>
        /// <param name="loadMode">加载模式 0=Single, 1=Additive</param>
        public void LoadScene(string sceneName, int loadMode = 0)
        {
            SceneLoadMode mode = (SceneLoadMode)loadMode;
            GameFramework.Core.GameFramework.Scene?.LoadScene(sceneName, mode);
        }

        /// <summary>
        /// 异步加载场景
        /// </summary>
        /// <param name="sceneName">场景名称</param>
        /// <param name="loadMode">加载模式 0=Single, 1=Additive</param>
        public void LoadSceneAsync(string sceneName, int loadMode = 0)
        {
            SceneLoadMode mode = (SceneLoadMode)loadMode;
            GameFramework.Core.GameFramework.Scene?.LoadSceneAsync(sceneName, mode);
        }

        /// <summary>
        /// 卸载场景
        /// </summary>
        /// <param name="sceneName">场景名称</param>
        public void UnloadScene(string sceneName)
        {
            GameFramework.Core.GameFramework.Scene?.UnloadScene(sceneName);
        }

        /// <summary>
        /// 注册场景加载进度事件
        /// </summary>
        /// <param name="callback">回调函数</param>
        public void OnSceneLoadProgress(System.Action<float> callback)
        {
            if (GameFramework.Core.GameFramework.Scene != null)
            {
                GameFramework.Core.GameFramework.Scene.OnSceneLoadProgress += callback;
            }
        }

        /// <summary>
        /// 注销场景加载进度事件
        /// </summary>
        /// <param name="callback">回调函数</param>
        public void OffSceneLoadProgress(System.Action<float> callback)
        {
            if (GameFramework.Core.GameFramework.Scene != null)
            {
                GameFramework.Core.GameFramework.Scene.OnSceneLoadProgress -= callback;
            }
        }

        /// <summary>
        /// 注册场景加载完成事件
        /// </summary>
        /// <param name="callback">回调函数</param>
        public void OnSceneLoaded(System.Action<string> callback)
        {
            if (GameFramework.Core.GameFramework.Scene != null)
            {
                GameFramework.Core.GameFramework.Scene.OnSceneLoaded += callback;
            }
        }

        /// <summary>
        /// 注销场景加载完成事件
        /// </summary>
        /// <param name="callback">回调函数</param>
        public void OffSceneLoaded(System.Action<string> callback)
        {
            if (GameFramework.Core.GameFramework.Scene != null)
            {
                GameFramework.Core.GameFramework.Scene.OnSceneLoaded -= callback;
            }
        }

        /// <summary>
        /// 检查场景是否已加载
        /// </summary>
        /// <param name="sceneName">场景名称</param>
        /// <returns>是否已加载</returns>
        public bool IsSceneLoaded(string sceneName)
        {
            var sceneManager = GameFramework.Core.GameFramework.Scene as GameFramework.Core.SceneManager;
            return sceneManager?.IsSceneLoaded(sceneName) ?? false;
        }

        /// <summary>
        /// 获取已加载的场景列表
        /// </summary>
        /// <returns>场景名称数组</returns>
        public string[] GetLoadedScenes()
        {
            var sceneManager = GameFramework.Core.GameFramework.Scene as GameFramework.Core.SceneManager;
            var sceneList = sceneManager?.GetLoadedScenes();
            return sceneList?.ToArray() ?? new string[0];
        }

        /// <summary>
        /// 检查是否为当前场景
        /// </summary>
        /// <param name="sceneName">场景名称</param>
        /// <returns>是否为当前场景</returns>
        public bool IsCurrentScene(string sceneName)
        {
            return CurrentSceneName == sceneName;
        }
    }

    /// <summary>
    /// Lua场景管理辅助类
    /// 提供一些便捷的场景操作方法
    /// </summary>
    public static class LuaSceneHelper
    {
        /// <summary>
        /// 加载主菜单场景
        /// </summary>
        public static void LoadMainMenu()
        {
            LuaGameFramework.Scene.LoadSceneAsync("MainMenu");
        }

        /// <summary>
        /// 加载游戏场景
        /// </summary>
        /// <param name="levelName">关卡名称</param>
        public static void LoadGameLevel(string levelName)
        {
            LuaGameFramework.Scene.LoadSceneAsync(levelName);
        }

        /// <summary>
        /// 重新加载当前场景
        /// </summary>
        public static void ReloadCurrentScene()
        {
            string currentScene = LuaGameFramework.Scene.CurrentSceneName;
            if (!string.IsNullOrEmpty(currentScene))
            {
                LuaGameFramework.Scene.LoadSceneAsync(currentScene);
            }
        }

        /// <summary>
        /// 加载设置场景
        /// </summary>
        public static void LoadSettings()
        {
            LuaGameFramework.Scene.LoadSceneAsync("Settings");
        }

        /// <summary>
        /// 返回主菜单
        /// </summary>
        public static void BackToMainMenu()
        {
            LoadMainMenu();
            LuaGameFramework.GameState.ChangeState(GameState.MainMenu);
        }

        /// <summary>
        /// 预加载场景（叠加模式）
        /// </summary>
        /// <param name="sceneName">场景名称</param>
        public static void PreloadScene(string sceneName)
        {
            LuaGameFramework.Scene.LoadSceneAsync(sceneName, 1); // Additive mode
        }

        /// <summary>
        /// 创建场景加载进度监听器
        /// </summary>
        /// <param name="onProgress">进度回调</param>
        /// <param name="onComplete">完成回调</param>
        /// <returns>监听器ID</returns>
        public static int CreateLoadProgressListener(System.Action<float> onProgress, System.Action<string> onComplete)
        {
            int listenerId = UnityEngine.Random.Range(1000, 9999);
            
            System.Action<float> progressWrapper = (progress) =>
            {
                onProgress?.Invoke(progress);
            };

            System.Action<string> completeWrapper = (sceneName) =>
            {
                onComplete?.Invoke(sceneName);
                // 自动注销事件
                LuaGameFramework.Scene.OffSceneLoadProgress(progressWrapper);
                LuaGameFramework.Scene.OffSceneLoaded(completeWrapper);
            };

            LuaGameFramework.Scene.OnSceneLoadProgress(progressWrapper);
            LuaGameFramework.Scene.OnSceneLoaded(completeWrapper);

            return listenerId;
        }

        /// <summary>
        /// 批量预加载场景
        /// </summary>
        /// <param name="sceneNames">场景名称数组</param>
        /// <param name="onAllComplete">全部完成回调</param>
        public static void PreloadScenes(string[] sceneNames, System.Action onAllComplete = null)
        {
            if (sceneNames == null || sceneNames.Length == 0)
            {
                onAllComplete?.Invoke();
                return;
            }

            int loadedCount = 0;
            int totalCount = sceneNames.Length;

            System.Action<string> onSceneLoaded = null;
            onSceneLoaded = (sceneName) =>
            {
                loadedCount++;
                Debug.Log($"[LuaSceneHelper] 预加载场景完成: {sceneName} ({loadedCount}/{totalCount})");

                if (loadedCount >= totalCount)
                {
                    LuaGameFramework.Scene.OffSceneLoaded(onSceneLoaded);
                    onAllComplete?.Invoke();
                    Debug.Log("[LuaSceneHelper] 所有场景预加载完成");
                }
            };

            LuaGameFramework.Scene.OnSceneLoaded(onSceneLoaded);

            // 开始加载所有场景
            foreach (string sceneName in sceneNames)
            {
                PreloadScene(sceneName);
            }
        }

        /// <summary>
        /// 获取场景加载状态信息
        /// </summary>
        /// <returns>状态信息字符串</returns>
        public static string GetSceneLoadStatus()
        {
            string currentScene = LuaGameFramework.Scene.CurrentSceneName;
            string[] loadedScenes = LuaGameFramework.Scene.GetLoadedScenes();
            
            return $"当前场景: {currentScene}, 已加载场景数: {loadedScenes.Length}";
        }

        /// <summary>
        /// 清理所有叠加场景
        /// </summary>
        public static void UnloadAllAdditiveScenes()
        {
            string[] loadedScenes = LuaGameFramework.Scene.GetLoadedScenes();
            string currentScene = LuaGameFramework.Scene.CurrentSceneName;

            foreach (string sceneName in loadedScenes)
            {
                if (sceneName != currentScene)
                {
                    LuaGameFramework.Scene.UnloadScene(sceneName);
                }
            }
        }
    }
}
